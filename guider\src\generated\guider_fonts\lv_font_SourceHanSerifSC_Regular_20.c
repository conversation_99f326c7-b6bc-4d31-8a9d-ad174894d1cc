/*
*This Font Software is licensed under the SIL Open Font License, Version 1.1. 
*This license is available with a FAQ at: http://scripts.sil.org/OFL
*/
/*******************************************************************************
 * Size: 20 px
 * Bpp: 4
 * Opts: undefined
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_SOURCEHANSERIFSC_REGULAR_20
#define LV_FONT_SOURCEHANSERIFSC_REGULAR_20 1
#endif

#if LV_FONT_SOURCEHANSERIFSC_REGULAR_20

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0020 " " */

    /* U+0021 "!" */
    0xa, 0xc0, 0xe, 0xf1, 0xd, 0xf0, 0xc, 0xf0,
    0xa, 0xd0, 0x8, 0xb0, 0x6, 0x90, 0x5, 0x80,
    0x4, 0x70, 0x3, 0x60, 0x1, 0x20, 0x0, 0x0,
    0x2, 0x30, 0xf, 0xf2, 0xb, 0xd0,

    /* U+0022 "\"" */
    0xb9, 0x2, 0xe4, 0xed, 0x5, 0xf7, 0xdb, 0x3,
    0xf5, 0xba, 0x1, 0xf3, 0x98, 0x0, 0xf2, 0x76,
    0x0, 0xd0, 0x43, 0x0, 0x70,

    /* U+0023 "#" */
    0x0, 0x0, 0xb0, 0x0, 0x65, 0x0, 0x0, 0x0,
    0xa0, 0x0, 0x82, 0x0, 0x0, 0x2, 0x80, 0x0,
    0xa0, 0x0, 0x0, 0x5, 0x60, 0x0, 0xb0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x4, 0x5c,
    0x65, 0x57, 0xb5, 0x50, 0x0, 0xb, 0x0, 0x4,
    0x70, 0x0, 0x0, 0xb, 0x0, 0x6, 0x40, 0x0,
    0x1, 0x2a, 0x11, 0x19, 0x31, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0x40, 0x24, 0x88, 0x44, 0x4c,
    0x44, 0x10, 0x0, 0x73, 0x0, 0xa, 0x0, 0x0,
    0x0, 0x91, 0x0, 0x19, 0x0, 0x0, 0x0, 0xb0,
    0x0, 0x47, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x65,
    0x0, 0x0,

    /* U+0024 "$" */
    0x0, 0x0, 0x50, 0x0, 0x0, 0x0, 0xa, 0x0,
    0x0, 0x0, 0x0, 0xa0, 0x0, 0x0, 0x7, 0xce,
    0xba, 0x30, 0xa, 0xa0, 0xa0, 0x5f, 0x23, 0xf1,
    0xa, 0x1, 0xf9, 0x6f, 0x0, 0xa0, 0x7, 0x65,
    0xf3, 0xa, 0x0, 0x0, 0x1e, 0xd2, 0xa0, 0x0,
    0x0, 0x5f, 0xfe, 0x30, 0x0, 0x0, 0x2b, 0xff,
    0xa1, 0x0, 0x0, 0xb, 0xaf, 0xd1, 0x0, 0x0,
    0xb0, 0x5f, 0x90, 0x0, 0xb, 0x0, 0xbd, 0x87,
    0x0, 0xb0, 0x8, 0xda, 0xf0, 0xb, 0x0, 0xb9,
    0x3f, 0x30, 0xb0, 0x6e, 0x10, 0x3a, 0xbe, 0xb9,
    0x10, 0x0, 0x0, 0xb0, 0x0, 0x0, 0x0, 0xb,
    0x0, 0x0, 0x0, 0x0, 0x50, 0x0, 0x0,

    /* U+0025 "%" */
    0x0, 0x89, 0xa4, 0x0, 0x0, 0x0, 0x2, 0x40,
    0x0, 0x6, 0xa0, 0xe, 0x20, 0x0, 0x0, 0xb,
    0x10, 0x0, 0xd, 0x50, 0xa, 0x80, 0x0, 0x0,
    0x74, 0x0, 0x0, 0xf, 0x40, 0x8, 0xa0, 0x0,
    0x2, 0x90, 0x0, 0x0, 0xf, 0x30, 0x8, 0xc0,
    0x0, 0xb, 0x10, 0x0, 0x0, 0xf, 0x40, 0x9,
    0xa0, 0x0, 0x74, 0x0, 0x0, 0x0, 0xc, 0x60,
    0xb, 0x70, 0x2, 0x90, 0x6, 0x88, 0x0, 0x4,
    0xc0, 0x1e, 0x10, 0xb, 0x10, 0x89, 0x5, 0xc0,
    0x0, 0x48, 0x82, 0x0, 0x74, 0x0, 0xf2, 0x0,
    0xe3, 0x0, 0x0, 0x0, 0x2, 0x90, 0x3, 0xf0,
    0x0, 0xc6, 0x0, 0x0, 0x0, 0xb, 0x10, 0x4,
    0xf0, 0x0, 0xb8, 0x0, 0x0, 0x0, 0x74, 0x0,
    0x3, 0xf0, 0x0, 0xc7, 0x0, 0x0, 0x2, 0x90,
    0x0, 0x1, 0xf1, 0x0, 0xe4, 0x0, 0x0, 0xb,
    0x10, 0x0, 0x0, 0xa6, 0x3, 0xd0, 0x0, 0x0,
    0x74, 0x0, 0x0, 0x0, 0x1a, 0x8a, 0x20, 0x0,
    0x0, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0026 "&" */
    0x0, 0x2, 0xaa, 0xaa, 0x20, 0x0, 0x0, 0x0,
    0x1, 0xe4, 0x0, 0x7d, 0x0, 0x0, 0x0, 0x0,
    0x5e, 0x0, 0x1, 0xf2, 0x0, 0x0, 0x0, 0x6,
    0xe0, 0x0, 0x2f, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0x40, 0x9, 0x80, 0x0, 0x0, 0x0, 0x0, 0x9d,
    0x18, 0x70, 0x3, 0x55, 0x54, 0x0, 0x1, 0xee,
    0x30, 0x0, 0x14, 0xd6, 0x20, 0x2, 0xa4, 0xec,
    0x0, 0x0, 0xd, 0x0, 0x3, 0xe2, 0x3, 0xfb,
    0x0, 0x3, 0x90, 0x0, 0xc9, 0x0, 0x5, 0xfa,
    0x0, 0x92, 0x0, 0xf, 0x60, 0x0, 0x6, 0xf9,
    0x18, 0x0, 0x1, 0xf8, 0x0, 0x0, 0x8, 0xfd,
    0x10, 0x0, 0xd, 0xe1, 0x0, 0x0, 0xd, 0xf8,
    0x0, 0x0, 0x3f, 0xd4, 0x1, 0x4a, 0x3b, 0xf7,
    0x0, 0x0, 0x2a, 0xdf, 0xd9, 0x10, 0x9, 0xed,
    0x70,

    /* U+0027 "'" */
    0xb9, 0xed, 0xdb, 0xba, 0x98, 0x76, 0x43,

    /* U+0028 "(" */
    0x0, 0x0, 0x26, 0x0, 0x0, 0xb1, 0x0, 0xa,
    0x60, 0x0, 0x3d, 0x0, 0x0, 0xc6, 0x0, 0x3,
    0xf0, 0x0, 0x8, 0xc0, 0x0, 0xc, 0x80, 0x0,
    0xf, 0x60, 0x0, 0xf, 0x50, 0x0, 0x2f, 0x40,
    0x0, 0x1f, 0x40, 0x0, 0xf, 0x50, 0x0, 0xe,
    0x70, 0x0, 0xa, 0xa0, 0x0, 0x5, 0xe0, 0x0,
    0x0, 0xf3, 0x0, 0x0, 0x7a, 0x0, 0x0, 0xe,
    0x20, 0x0, 0x4, 0xb0, 0x0, 0x0, 0x75, 0x0,
    0x0, 0x2,

    /* U+0029 ")" */
    0x35, 0x0, 0x0, 0xa, 0x30, 0x0, 0x2, 0xd0,
    0x0, 0x0, 0x98, 0x0, 0x0, 0x2f, 0x10, 0x0,
    0xb, 0x70, 0x0, 0x7, 0xc0, 0x0, 0x3, 0xf1,
    0x0, 0x1, 0xf3, 0x0, 0x0, 0xf5, 0x0, 0x0,
    0xf6, 0x0, 0x0, 0xf6, 0x0, 0x0, 0xf4, 0x0,
    0x2, 0xf3, 0x0, 0x5, 0xe0, 0x0, 0x9, 0xa0,
    0x0, 0xe, 0x40, 0x0, 0x5c, 0x0, 0x0, 0xc3,
    0x0, 0x6, 0x80, 0x0, 0x1a, 0x0, 0x0, 0x11,
    0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x0, 0xb5, 0x0, 0x0, 0x0, 0x0, 0xd6,
    0x0, 0x0, 0x1, 0x0, 0xb4, 0x0, 0x20, 0x7f,
    0xa2, 0x82, 0x6d, 0xf1, 0x4, 0x7a, 0xb9, 0x96,
    0x20, 0x0, 0x3, 0x8a, 0x0, 0x0, 0x0, 0x3e,
    0x17, 0xb0, 0x0, 0x0, 0xe9, 0x1, 0xe8, 0x0,
    0x0, 0x81, 0x0, 0x55, 0x0,

    /* U+002B "+" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa4, 0x0, 0x0, 0x0, 0x0, 0xa, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xa4, 0x0, 0x0, 0x0, 0x0,
    0xa, 0x40, 0x0, 0x0, 0x0, 0x0, 0xa4, 0x0,
    0x0, 0x3d, 0xdd, 0xdf, 0xed, 0xdd, 0xc0, 0x0,
    0x0, 0xa4, 0x0, 0x0, 0x0, 0x0, 0xa, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xa4, 0x0, 0x0, 0x0,
    0x0, 0xa, 0x40, 0x0, 0x0, 0x0, 0x0, 0x93,
    0x0, 0x0,

    /* U+002C "," */
    0x8, 0xe3, 0xc, 0xf7, 0x2, 0xd5, 0x0, 0xc1,
    0x9, 0x60, 0x64, 0x0,

    /* U+002D "-" */
    0x2e, 0xee, 0xee, 0x0, 0x11, 0x11, 0x10,

    /* U+002E "." */
    0x36, 0xd, 0xf6, 0x8d, 0x20,

    /* U+002F "/" */
    0x0, 0x0, 0x2, 0xb0, 0x0, 0x0, 0x76, 0x0,
    0x0, 0xc, 0x20, 0x0, 0x1, 0xd0, 0x0, 0x0,
    0x58, 0x0, 0x0, 0xa, 0x30, 0x0, 0x0, 0xd0,
    0x0, 0x0, 0x3a, 0x0, 0x0, 0x8, 0x50, 0x0,
    0x0, 0xd1, 0x0, 0x0, 0x2b, 0x0, 0x0, 0x7,
    0x70, 0x0, 0x0, 0xb2, 0x0, 0x0, 0x1d, 0x0,
    0x0, 0x5, 0x80, 0x0, 0x0, 0xa4, 0x0, 0x0,
    0xe, 0x0, 0x0, 0x3, 0xa0, 0x0, 0x0, 0x85,
    0x0, 0x0, 0x6, 0x10, 0x0, 0x0,

    /* U+0030 "0" */
    0x0, 0x4, 0xbb, 0xb5, 0x0, 0x0, 0x5, 0xe2,
    0x1, 0xd7, 0x0, 0x1, 0xf5, 0x0, 0x4, 0xf3,
    0x0, 0x7f, 0x0, 0x0, 0xe, 0x90, 0xc, 0xc0,
    0x0, 0x0, 0xbe, 0x0, 0xea, 0x0, 0x0, 0x9,
    0xf0, 0xf, 0xa0, 0x0, 0x0, 0x8f, 0x20, 0xf9,
    0x0, 0x0, 0x7, 0xf2, 0xf, 0xa0, 0x0, 0x0,
    0x8f, 0x20, 0xeb, 0x0, 0x0, 0x9, 0xf0, 0xc,
    0xd0, 0x0, 0x0, 0xbd, 0x0, 0x7f, 0x0, 0x0,
    0xe, 0x90, 0x1, 0xf5, 0x0, 0x4, 0xf3, 0x0,
    0x5, 0xe1, 0x1, 0xd7, 0x0, 0x0, 0x4, 0xba,
    0xb5, 0x0, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x30, 0x0, 0x27, 0xbf, 0xf0, 0x0,
    0x34, 0x1b, 0xf0, 0x0, 0x0, 0xb, 0xe0, 0x0,
    0x0, 0xb, 0xe0, 0x0, 0x0, 0xb, 0xe0, 0x0,
    0x0, 0xb, 0xe0, 0x0, 0x0, 0xb, 0xe0, 0x0,
    0x0, 0xb, 0xe0, 0x0, 0x0, 0xb, 0xe0, 0x0,
    0x0, 0xb, 0xe0, 0x0, 0x0, 0xb, 0xe0, 0x0,
    0x0, 0xb, 0xe0, 0x0, 0x0, 0xb, 0xe0, 0x0,
    0x0, 0xc, 0xf0, 0x0, 0x4b, 0xcf, 0xfd, 0xb5,

    /* U+0032 "2" */
    0x1, 0x79, 0xac, 0x91, 0x0, 0x1d, 0x60, 0x1,
    0xdd, 0x0, 0x7f, 0x20, 0x0, 0x5f, 0x60, 0x68,
    0x0, 0x0, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x2f,
    0x80, 0x0, 0x0, 0x0, 0x6f, 0x30, 0x0, 0x0,
    0x0, 0xda, 0x0, 0x0, 0x0, 0x8, 0xd0, 0x0,
    0x0, 0x0, 0x4e, 0x20, 0x0, 0x0, 0x1, 0xd2,
    0x0, 0x0, 0x0, 0xc, 0x30, 0x0, 0x0, 0x0,
    0xa4, 0x0, 0x0, 0x0, 0x8, 0x50, 0x0, 0x0,
    0x0, 0x6c, 0x77, 0x77, 0x77, 0x71, 0xbf, 0xff,
    0xff, 0xff, 0xf3,

    /* U+0033 "3" */
    0x0, 0x29, 0x9a, 0xc9, 0x10, 0x2, 0xf5, 0x0,
    0x1d, 0xd0, 0x6, 0xd0, 0x0, 0x6, 0xf4, 0x0,
    0x0, 0x0, 0x5, 0xf5, 0x0, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x0, 0x0, 0x7e, 0x40, 0x0, 0x4,
    0xcf, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x5e, 0x70,
    0x0, 0x0, 0x0, 0x5, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xed, 0x8,
    0x30, 0x0, 0x0, 0xfb, 0xd, 0xb0, 0x0, 0x4,
    0xf6, 0x5, 0xf0, 0x0, 0x2d, 0xb0, 0x0, 0x49,
    0x9b, 0xc6, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x0, 0x3f, 0x40, 0x0, 0x0, 0x0,
    0xc, 0xf4, 0x0, 0x0, 0x0, 0x8, 0x5f, 0x40,
    0x0, 0x0, 0x4, 0x63, 0xf4, 0x0, 0x0, 0x1,
    0x90, 0x3f, 0x40, 0x0, 0x0, 0x91, 0x3, 0xf4,
    0x0, 0x0, 0x64, 0x0, 0x3f, 0x40, 0x0, 0x28,
    0x0, 0x3, 0xf4, 0x0, 0xa, 0x0, 0x0, 0x3f,
    0x40, 0x4, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x2,
    0x22, 0x22, 0x5f, 0x62, 0x20, 0x0, 0x0, 0x3,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x40, 0x0,
    0x0, 0x0, 0x3, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0x40, 0x0,

    /* U+0035 "5" */
    0x0, 0x8f, 0xff, 0xff, 0xfa, 0x0, 0x9, 0x87,
    0x77, 0x77, 0x40, 0x0, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0xb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xda, 0xcd, 0xb7, 0x0, 0x0, 0x4,
    0x20, 0x4, 0xde, 0x10, 0x0, 0x0, 0x0, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x0, 0x41, 0x0, 0x0,
    0xd, 0xd0, 0xf, 0x90, 0x0, 0x2, 0xf8, 0x0,
    0x7e, 0x0, 0x2, 0xcc, 0x0, 0x0, 0x6a, 0xaa,
    0xb6, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x3,
    0xab, 0x50, 0x0, 0x0, 0x8c, 0x20, 0x0, 0x0,
    0xb, 0x90, 0x0, 0x0, 0x0, 0xcb, 0x0, 0x0,
    0x0, 0x7, 0xf1, 0x0, 0x0, 0x0, 0xe, 0x80,
    0x0, 0x0, 0x0, 0x5f, 0x47, 0xbd, 0xa2, 0x0,
    0x9f, 0x94, 0x1, 0xaf, 0x30, 0xce, 0x10, 0x0,
    0xe, 0xb0, 0xdd, 0x0, 0x0, 0xa, 0xf0, 0xce,
    0x0, 0x0, 0x8, 0xf2, 0xaf, 0x0, 0x0, 0x8,
    0xf1, 0x7f, 0x30, 0x0, 0xa, 0xe0, 0x1f, 0x80,
    0x0, 0xe, 0x80, 0x6, 0xf3, 0x0, 0x9d, 0x0,
    0x0, 0x4b, 0xaa, 0x90, 0x0,

    /* U+0037 "7" */
    0xef, 0xff, 0xff, 0xff, 0xc6, 0x77, 0x77, 0x77,
    0x98, 0x0, 0x0, 0x0, 0x9, 0x20, 0x0, 0x0,
    0x1, 0xc0, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0,
    0x0, 0xe, 0x10, 0x0, 0x0, 0x6, 0xb0, 0x0,
    0x0, 0x0, 0xd5, 0x0, 0x0, 0x0, 0x4e, 0x0,
    0x0, 0x0, 0xb, 0x90, 0x0, 0x0, 0x2, 0xf3,
    0x0, 0x0, 0x0, 0x9d, 0x0, 0x0, 0x0, 0x1f,
    0x70, 0x0, 0x0, 0x7, 0xf1, 0x0, 0x0, 0x0,
    0xeb, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x7b, 0xab, 0x91, 0x0, 0xa, 0xb0, 0x0,
    0xad, 0x0, 0x3f, 0x30, 0x0, 0x1f, 0x40, 0x6f,
    0x10, 0x0, 0xf, 0x60, 0x4f, 0x40, 0x0, 0x2f,
    0x30, 0xe, 0xd1, 0x0, 0x78, 0x0, 0x3, 0xed,
    0x43, 0x60, 0x0, 0x0, 0x1a, 0xcf, 0x70, 0x0,
    0x3, 0x80, 0x6, 0xfc, 0x10, 0x2e, 0x0, 0x0,
    0x4f, 0x90, 0xaa, 0x0, 0x0, 0xb, 0xf0, 0xda,
    0x0, 0x0, 0x8, 0xf0, 0xad, 0x0, 0x0, 0xb,
    0xc0, 0x3f, 0x70, 0x0, 0x5f, 0x30, 0x2, 0xab,
    0xaa, 0xa2, 0x0,

    /* U+0039 "9" */
    0x0, 0x7b, 0xab, 0x70, 0x0, 0xc, 0xb0, 0x0,
    0xcb, 0x0, 0x7f, 0x10, 0x0, 0x2f, 0x50, 0xbd,
    0x0, 0x0, 0xd, 0xc0, 0xec, 0x0, 0x0, 0xa,
    0xf0, 0xce, 0x0, 0x0, 0x9, 0xf1, 0x8f, 0x40,
    0x0, 0x9, 0xf1, 0xd, 0xe4, 0x0, 0x6e, 0xf0,
    0x0, 0x8c, 0xda, 0x4f, 0xb0, 0x0, 0x0, 0x0,
    0x4f, 0x50, 0x0, 0x0, 0x0, 0xcd, 0x0, 0x0,
    0x0, 0x6, 0xf3, 0x0, 0x0, 0x0, 0x6e, 0x30,
    0x0, 0x0, 0x8, 0xd2, 0x0, 0x0, 0x7, 0xa4,
    0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,

    /* U+003A ":" */
    0x8d, 0x2d, 0xf6, 0x36, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x36, 0xd, 0xf6, 0x8d,
    0x20,

    /* U+003B ";" */
    0x8, 0xd2, 0xd, 0xf6, 0x3, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xe3, 0xd, 0xf7, 0x2, 0xd5, 0x0, 0xc1,
    0x9, 0x60, 0x64, 0x0,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x2, 0xa1, 0x0, 0x0, 0x0,
    0x8d, 0x40, 0x0, 0x0, 0x5d, 0x70, 0x0, 0x0,
    0x2b, 0xa1, 0x0, 0x0, 0x19, 0xc3, 0x0, 0x0,
    0x0, 0xba, 0x0, 0x0, 0x0, 0x0, 0x1a, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x3c, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x6d, 0x50, 0x0, 0x0, 0x0, 0x1,
    0x9c, 0x30, 0x0, 0x0, 0x0, 0x3, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+003D "=" */
    0x3d, 0xdd, 0xdd, 0xdd, 0xdd, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdc,

    /* U+003E ">" */
    0x67, 0x0, 0x0, 0x0, 0x0, 0x8, 0xd4, 0x0,
    0x0, 0x0, 0x0, 0x2b, 0xb2, 0x0, 0x0, 0x0,
    0x0, 0x4d, 0x70, 0x0, 0x0, 0x0, 0x0, 0x7d,
    0x40, 0x0, 0x0, 0x0, 0x2, 0xe5, 0x0, 0x0,
    0x0, 0x5d, 0x60, 0x0, 0x0, 0x3c, 0x91, 0x0,
    0x0, 0x19, 0xc3, 0x0, 0x0, 0x7, 0xd6, 0x0,
    0x0, 0x0, 0x79, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x7, 0x40, 0x0, 0x4, 0xff, 0xd4, 0x0, 0x5,
    0x9b, 0xd5, 0x0, 0x0, 0x0, 0xa1, 0x0, 0x0,
    0x6, 0x50, 0x0, 0x0, 0x96, 0x0, 0x0, 0x5f,
    0x30, 0x4, 0xcf, 0x80, 0x4, 0xfb, 0x30, 0x0,
    0x7a, 0x0, 0x0, 0x4, 0x70, 0x0, 0x0, 0x3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x24, 0x0,
    0x0, 0xd, 0xf4, 0x0, 0x0, 0x9d, 0x20, 0x0,

    /* U+0040 "@" */
    0x0, 0x0, 0x0, 0x27, 0xaa, 0xbc, 0xb4, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xa3, 0x0, 0x0, 0x18,
    0x90, 0x0, 0x0, 0x1, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x3b, 0x0, 0x0, 0xc, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x7, 0x50, 0x0, 0x86, 0x0, 0x0,
    0x7d, 0xd6, 0x90, 0x0, 0xb0, 0x1, 0xc0, 0x0,
    0xa, 0x90, 0xb, 0xc0, 0x0, 0xa1, 0x8, 0x50,
    0x0, 0x7b, 0x0, 0xa, 0x90, 0x0, 0x73, 0xc,
    0x10, 0x0, 0xe3, 0x0, 0xd, 0x70, 0x0, 0x55,
    0xd, 0x0, 0x6, 0xd0, 0x0, 0xf, 0x40, 0x0,
    0x73, 0x1c, 0x0, 0x9, 0xb0, 0x0, 0x3f, 0x10,
    0x0, 0xa1, 0x2c, 0x0, 0xc, 0x90, 0x0, 0x5e,
    0x0, 0x0, 0xb0, 0xd, 0x0, 0xb, 0xb0, 0x0,
    0xbc, 0x0, 0x8, 0x30, 0xd, 0x10, 0x7, 0xf6,
    0x48, 0x5d, 0x0, 0x77, 0x0, 0x8, 0x80, 0x0,
    0x8c, 0x81, 0x8, 0xb9, 0x30, 0x0, 0x0, 0xd3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3d, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xb9, 0x30, 0x0, 0x3, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x7a, 0xa9, 0x84, 0x0,
    0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x9d, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x7f, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x91, 0xcd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0x7, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x70, 0x2f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x82,
    0x0, 0xdd, 0x0, 0x0, 0x0, 0x0, 0xb, 0x0,
    0x8, 0xf2, 0x0, 0x0, 0x0, 0x3, 0x80, 0x0,
    0x3f, 0x70, 0x0, 0x0, 0x0, 0x8b, 0xaa, 0xaa,
    0xfd, 0x0, 0x0, 0x0, 0xc, 0x0, 0x0, 0x9,
    0xf2, 0x0, 0x0, 0x3, 0x90, 0x0, 0x0, 0x4f,
    0x70, 0x0, 0x0, 0x84, 0x0, 0x0, 0x0, 0xec,
    0x0, 0x0, 0xc, 0x0, 0x0, 0x0, 0xa, 0xf2,
    0x0, 0x8c, 0xfc, 0x70, 0x0, 0x6b, 0xef, 0xda,
    0x20,

    /* U+0042 "B" */
    0x9b, 0xff, 0xab, 0xbc, 0x81, 0x0, 0x0, 0xde,
    0x0, 0x2, 0xcd, 0x0, 0x0, 0xde, 0x0, 0x0,
    0x3f, 0x70, 0x0, 0xde, 0x0, 0x0, 0xf, 0xa0,
    0x0, 0xde, 0x0, 0x0, 0x1f, 0x90, 0x0, 0xde,
    0x0, 0x0, 0x7f, 0x30, 0x0, 0xde, 0x0, 0x6,
    0xe4, 0x0, 0x0, 0xdf, 0x9b, 0xec, 0x40, 0x0,
    0x0, 0xde, 0x0, 0x2, 0x9d, 0x40, 0x0, 0xde,
    0x0, 0x0, 0xb, 0xf1, 0x0, 0xde, 0x0, 0x0,
    0x6, 0xf6, 0x0, 0xde, 0x0, 0x0, 0x5, 0xf6,
    0x0, 0xde, 0x0, 0x0, 0xa, 0xf2, 0x0, 0xde,
    0x0, 0x0, 0x7f, 0x70, 0x9c, 0xff, 0xab, 0xbb,
    0x92, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x6b, 0xdc, 0xab, 0x50, 0x0, 0x2c,
    0xd3, 0x0, 0x1, 0xd9, 0x2, 0xfa, 0x0, 0x0,
    0x0, 0x99, 0xb, 0xf1, 0x0, 0x0, 0x0, 0x69,
    0x4f, 0x80, 0x0, 0x0, 0x0, 0x12, 0x9f, 0x40,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0x20, 0x0, 0x0, 0x0, 0x0, 0xaf, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x80, 0x0, 0x0,
    0x0, 0x3, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x4c,
    0x2, 0xfa, 0x0, 0x0, 0x0, 0x7c, 0x0, 0x3d,
    0xc2, 0x0, 0x0, 0xbb, 0x0, 0x0, 0x7c, 0xdb,
    0xab, 0x60,

    /* U+0044 "D" */
    0x9b, 0xff, 0xab, 0xbb, 0x93, 0x0, 0x0, 0x0,
    0xde, 0x0, 0x0, 0x5f, 0x90, 0x0, 0x0, 0xde,
    0x0, 0x0, 0x2, 0xfa, 0x0, 0x0, 0xde, 0x0,
    0x0, 0x0, 0x8f, 0x40, 0x0, 0xde, 0x0, 0x0,
    0x0, 0x1f, 0xc0, 0x0, 0xde, 0x0, 0x0, 0x0,
    0xd, 0xf0, 0x0, 0xde, 0x0, 0x0, 0x0, 0xb,
    0xf1, 0x0, 0xde, 0x0, 0x0, 0x0, 0xa, 0xf3,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xc, 0xf2, 0x0,
    0xde, 0x0, 0x0, 0x0, 0xe, 0xf0, 0x0, 0xde,
    0x0, 0x0, 0x0, 0x1f, 0xc0, 0x0, 0xde, 0x0,
    0x0, 0x0, 0x9f, 0x30, 0x0, 0xde, 0x0, 0x0,
    0x3, 0xf9, 0x0, 0x0, 0xde, 0x0, 0x0, 0x7f,
    0x70, 0x0, 0x9c, 0xff, 0xab, 0xcd, 0x92, 0x0,
    0x0,

    /* U+0045 "E" */
    0x9b, 0xff, 0xbb, 0xbb, 0xbe, 0xb0, 0x0, 0xde,
    0x0, 0x0, 0x7, 0xb0, 0x0, 0xde, 0x0, 0x0,
    0x4, 0xc0, 0x0, 0xde, 0x0, 0x0, 0x1, 0xa0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0x0, 0x0, 0xde,
    0x0, 0x5, 0x60, 0x0, 0x0, 0xde, 0x0, 0x7,
    0x60, 0x0, 0x0, 0xdf, 0xaa, 0xad, 0x60, 0x0,
    0x0, 0xde, 0x0, 0x7, 0x60, 0x0, 0x0, 0xde,
    0x0, 0x5, 0x60, 0x0, 0x0, 0xde, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xde, 0x0, 0x0, 0x0, 0x81,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xe1, 0x0, 0xde,
    0x0, 0x0, 0x1, 0xf1, 0x9b, 0xff, 0xbb, 0xbb,
    0xbc, 0xf0,

    /* U+0046 "F" */
    0x9b, 0xff, 0xbb, 0xbb, 0xbd, 0xb0, 0xd, 0xe0,
    0x0, 0x0, 0x6c, 0x0, 0xde, 0x0, 0x0, 0x3,
    0xd0, 0xd, 0xe0, 0x0, 0x0, 0xa, 0x0, 0xde,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0, 0x47,
    0x0, 0x0, 0xde, 0x0, 0x6, 0x70, 0x0, 0xd,
    0xfa, 0xaa, 0xd7, 0x0, 0x0, 0xde, 0x0, 0x6,
    0x70, 0x0, 0xd, 0xe0, 0x0, 0x47, 0x0, 0x0,
    0xde, 0x0, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0xde, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xe0, 0x0, 0x0, 0x0, 0x9b, 0xff, 0xca,
    0x20, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x6b, 0xdc, 0xbb, 0x60, 0x0, 0x0,
    0x2c, 0xd3, 0x0, 0x1, 0xbc, 0x0, 0x2, 0xfb,
    0x0, 0x0, 0x0, 0x6d, 0x0, 0xb, 0xf1, 0x0,
    0x0, 0x0, 0x3d, 0x0, 0x5f, 0x80, 0x0, 0x0,
    0x0, 0x4, 0x0, 0xaf, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x0, 0x0, 0x0, 0x22, 0x22, 0x21,
    0xcf, 0x20, 0x0, 0x0, 0x78, 0xdf, 0x94, 0xaf,
    0x40, 0x0, 0x0, 0x0, 0xaf, 0x10, 0x5f, 0x80,
    0x0, 0x0, 0x0, 0xaf, 0x10, 0xc, 0xf1, 0x0,
    0x0, 0x0, 0x9f, 0x10, 0x2, 0xfa, 0x0, 0x0,
    0x0, 0x9f, 0x10, 0x0, 0x3c, 0xc2, 0x0, 0x0,
    0xae, 0x0, 0x0, 0x0, 0x7c, 0xdb, 0xab, 0x60,
    0x0,

    /* U+0048 "H" */
    0xac, 0xff, 0xca, 0x0, 0xa, 0xcf, 0xfc, 0xa0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xd, 0xf0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xd, 0xf0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0xdf, 0xaa, 0xaa, 0xaa, 0xaf, 0xe0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xd, 0xf0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0xd, 0xf0, 0x0,
    0x9b, 0xff, 0xba, 0x0, 0x9, 0xbf, 0xfb, 0xa0,

    /* U+0049 "I" */
    0x9b, 0xff, 0xba, 0x0, 0xd, 0xf0, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0xd, 0xe0, 0x0, 0x0, 0xde,
    0x0, 0x0, 0xd, 0xe0, 0x0, 0x0, 0xde, 0x0,
    0x0, 0xd, 0xe0, 0x0, 0x0, 0xde, 0x0, 0x0,
    0xd, 0xe0, 0x0, 0x0, 0xde, 0x0, 0x0, 0xd,
    0xe0, 0x0, 0x0, 0xdf, 0x0, 0x0, 0xd, 0xf0,
    0x0, 0x9b, 0xff, 0xba, 0x0,

    /* U+004A "J" */
    0x0, 0x9b, 0xef, 0xcb, 0x30, 0x0, 0x8, 0xf3,
    0x0, 0x0, 0x0, 0x8f, 0x30, 0x0, 0x0, 0x8,
    0xf3, 0x0, 0x0, 0x0, 0x8f, 0x30, 0x0, 0x0,
    0x8, 0xf3, 0x0, 0x0, 0x0, 0x8f, 0x30, 0x0,
    0x0, 0x7, 0xf3, 0x0, 0x0, 0x0, 0x7f, 0x30,
    0x0, 0x0, 0x7, 0xf3, 0x0, 0x0, 0x0, 0x7f,
    0x30, 0x0, 0x0, 0x7, 0xf3, 0x0, 0x0, 0x0,
    0x8f, 0x20, 0x0, 0x0, 0x8, 0xf1, 0x0, 0x0,
    0x0, 0xaf, 0x0, 0x0, 0x0, 0xc, 0xa0, 0x0,
    0xa, 0x82, 0xe2, 0x0, 0x1, 0xcf, 0xb2, 0x0,
    0x0,

    /* U+004B "K" */
    0x9b, 0xff, 0xba, 0x0, 0x9c, 0xfd, 0xa0, 0x0,
    0xdd, 0x0, 0x0, 0x4, 0xa0, 0x0, 0x0, 0xdd,
    0x0, 0x0, 0x2b, 0x0, 0x0, 0x0, 0xdd, 0x0,
    0x0, 0xb1, 0x0, 0x0, 0x0, 0xdd, 0x0, 0xa,
    0x30, 0x0, 0x0, 0x0, 0xdd, 0x0, 0x77, 0x0,
    0x0, 0x0, 0x0, 0xdd, 0x4, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xdd, 0x2a, 0x9f, 0x40, 0x0, 0x0,
    0x0, 0xde, 0xb1, 0x1f, 0xc0, 0x0, 0x0, 0x0,
    0xdf, 0x30, 0x8, 0xf5, 0x0, 0x0, 0x0, 0xdd,
    0x0, 0x0, 0xed, 0x0, 0x0, 0x0, 0xdd, 0x0,
    0x0, 0x6f, 0x70, 0x0, 0x0, 0xdd, 0x0, 0x0,
    0xd, 0xe0, 0x0, 0x0, 0xdd, 0x0, 0x0, 0x5,
    0xf8, 0x0, 0x9b, 0xff, 0xb9, 0x0, 0x8b, 0xff,
    0xb6,

    /* U+004C "L" */
    0x9b, 0xff, 0xba, 0x0, 0x0, 0x0, 0xd, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0xde, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xde,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0xde, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0xde, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0xde, 0x0, 0x0, 0x0, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0x3c, 0x0, 0xde, 0x0, 0x0, 0x5, 0xb0,
    0xd, 0xe0, 0x0, 0x0, 0x8b, 0x9c, 0xff, 0xbb,
    0xbb, 0xbe, 0xa0,

    /* U+004D "M" */
    0x1a, 0xcf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xdb, 0x40, 0x2, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x1f, 0xf5, 0x0, 0x0, 0x2a, 0xfa, 0x0, 0x0,
    0x0, 0x7, 0xaf, 0x50, 0x0, 0x2, 0x8c, 0xf1,
    0x0, 0x0, 0x0, 0xb6, 0xf5, 0x0, 0x0, 0x28,
    0x5f, 0x60, 0x0, 0x0, 0x38, 0x5f, 0x50, 0x0,
    0x2, 0x90, 0xed, 0x0, 0x0, 0x9, 0x26, 0xf5,
    0x0, 0x0, 0x29, 0x8, 0xf3, 0x0, 0x0, 0xb0,
    0x6f, 0x50, 0x0, 0x2, 0x90, 0x2f, 0x90, 0x0,
    0x66, 0x6, 0xf5, 0x0, 0x0, 0x29, 0x0, 0xcf,
    0x0, 0xb, 0x0, 0x6f, 0x50, 0x0, 0x2, 0x90,
    0x5, 0xf6, 0x2, 0x90, 0x6, 0xf5, 0x0, 0x0,
    0x29, 0x0, 0xe, 0xc0, 0x83, 0x0, 0x6f, 0x50,
    0x0, 0x2, 0x90, 0x0, 0x9f, 0x3b, 0x0, 0x6,
    0xf5, 0x0, 0x0, 0x29, 0x0, 0x2, 0xfd, 0x70,
    0x0, 0x6f, 0x50, 0x0, 0x2, 0x90, 0x0, 0xc,
    0xf1, 0x0, 0x6, 0xf5, 0x0, 0x1b, 0xde, 0xb7,
    0x0, 0x6a, 0x0, 0x4b, 0xdf, 0xdb, 0x40,

    /* U+004E "N" */
    0x1a, 0xcf, 0xb0, 0x0, 0x0, 0xac, 0xed, 0xb1,
    0x0, 0x3f, 0xf5, 0x0, 0x0, 0x0, 0x82, 0x0,
    0x0, 0x3a, 0xee, 0x10, 0x0, 0x0, 0x82, 0x0,
    0x0, 0x38, 0x5f, 0xa0, 0x0, 0x0, 0x82, 0x0,
    0x0, 0x38, 0xa, 0xf5, 0x0, 0x0, 0x82, 0x0,
    0x0, 0x38, 0x1, 0xee, 0x10, 0x0, 0x82, 0x0,
    0x0, 0x39, 0x0, 0x5f, 0xa0, 0x0, 0x82, 0x0,
    0x0, 0x39, 0x0, 0xa, 0xf4, 0x0, 0x82, 0x0,
    0x0, 0x39, 0x0, 0x1, 0xee, 0x0, 0x82, 0x0,
    0x0, 0x39, 0x0, 0x0, 0x5f, 0x90, 0x82, 0x0,
    0x0, 0x39, 0x0, 0x0, 0xa, 0xf4, 0x82, 0x0,
    0x0, 0x39, 0x0, 0x0, 0x1, 0xed, 0x92, 0x0,
    0x0, 0x39, 0x0, 0x0, 0x0, 0x5f, 0xf2, 0x0,
    0x0, 0x39, 0x0, 0x0, 0x0, 0xa, 0xf2, 0x0,
    0x1b, 0xde, 0xca, 0x0, 0x0, 0x1, 0xe2, 0x0,

    /* U+004F "O" */
    0x0, 0x1, 0x9c, 0xbc, 0xb4, 0x0, 0x0, 0x0,
    0x4e, 0x70, 0x0, 0x3e, 0x80, 0x0, 0x3, 0xf7,
    0x0, 0x0, 0x3, 0xf9, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0x0, 0x9f, 0x30, 0x5f, 0x70, 0x0, 0x0,
    0x0, 0x2f, 0xb0, 0xaf, 0x30, 0x0, 0x0, 0x0,
    0xe, 0xf0, 0xcf, 0x20, 0x0, 0x0, 0x0, 0xd,
    0xf1, 0xdf, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2,
    0xcf, 0x10, 0x0, 0x0, 0x0, 0xd, 0xf1, 0xaf,
    0x30, 0x0, 0x0, 0x0, 0xe, 0xf0, 0x6f, 0x70,
    0x0, 0x0, 0x0, 0x2f, 0xb0, 0xd, 0xd0, 0x0,
    0x0, 0x0, 0x8f, 0x30, 0x4, 0xf6, 0x0, 0x0,
    0x2, 0xe9, 0x0, 0x0, 0x5e, 0x60, 0x0, 0x3e,
    0x80, 0x0, 0x0, 0x1, 0x9a, 0xbb, 0xb4, 0x0,
    0x0,

    /* U+0050 "P" */
    0x9b, 0xff, 0xab, 0xbb, 0x70, 0x0, 0x0, 0xde,
    0x0, 0x1, 0xbe, 0x10, 0x0, 0xde, 0x0, 0x0,
    0x1f, 0xa0, 0x0, 0xde, 0x0, 0x0, 0xc, 0xe0,
    0x0, 0xde, 0x0, 0x0, 0xa, 0xf0, 0x0, 0xde,
    0x0, 0x0, 0xd, 0xd0, 0x0, 0xde, 0x0, 0x0,
    0x4f, 0x70, 0x0, 0xde, 0x0, 0x6, 0xe9, 0x0,
    0x0, 0xdf, 0xab, 0xa8, 0x20, 0x0, 0x0, 0xde,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xde, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xde, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xde, 0x0, 0x0, 0x0, 0x0, 0x0, 0xde,
    0x0, 0x0, 0x0, 0x0, 0x9c, 0xff, 0xca, 0x20,
    0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x1, 0x9c, 0xbc, 0xa3, 0x0, 0x0, 0x0,
    0x3e, 0x80, 0x0, 0x4e, 0x70, 0x0, 0x2, 0xf8,
    0x0, 0x0, 0x3, 0xf8, 0x0, 0xb, 0xe0, 0x0,
    0x0, 0x0, 0x9f, 0x20, 0x4f, 0x80, 0x0, 0x0,
    0x0, 0x3f, 0xa0, 0x9f, 0x40, 0x0, 0x0, 0x0,
    0xf, 0xe0, 0xcf, 0x20, 0x0, 0x0, 0x0, 0xd,
    0xf1, 0xdf, 0x0, 0x0, 0x0, 0x0, 0xb, 0xf2,
    0xcf, 0x10, 0x0, 0x0, 0x0, 0xc, 0xf1, 0xaf,
    0x30, 0x0, 0x0, 0x0, 0xe, 0xf0, 0x7f, 0x50,
    0x0, 0x0, 0x0, 0xf, 0xc0, 0x2f, 0xa0, 0x0,
    0x0, 0x0, 0x5f, 0x70, 0xa, 0xf1, 0x0, 0x0,
    0x0, 0xbe, 0x0, 0x1, 0xdc, 0x0, 0x0, 0x7,
    0xf4, 0x0, 0x0, 0x1b, 0xc3, 0x12, 0x8e, 0x30,
    0x0, 0x0, 0x0, 0x29, 0xfb, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0x90, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xad, 0xb8, 0x10,

    /* U+0052 "R" */
    0x9b, 0xff, 0xab, 0xbc, 0xa3, 0x0, 0x0, 0xd,
    0xe0, 0x0, 0x8, 0xf5, 0x0, 0x0, 0xde, 0x0,
    0x0, 0xc, 0xe0, 0x0, 0xd, 0xe0, 0x0, 0x0,
    0x8f, 0x20, 0x0, 0xde, 0x0, 0x0, 0x9, 0xf1,
    0x0, 0xd, 0xe0, 0x0, 0x1, 0xeb, 0x0, 0x0,
    0xde, 0x0, 0x2, 0xcb, 0x10, 0x0, 0xd, 0xfa,
    0xae, 0xe4, 0x0, 0x0, 0x0, 0xde, 0x0, 0xa,
    0xe2, 0x0, 0x0, 0xd, 0xe0, 0x0, 0x1f, 0xa0,
    0x0, 0x0, 0xde, 0x0, 0x0, 0xbf, 0x0, 0x0,
    0xd, 0xe0, 0x0, 0x6, 0xf4, 0x0, 0x0, 0xde,
    0x0, 0x0, 0x2f, 0x80, 0x0, 0xd, 0xe0, 0x0,
    0x0, 0xdd, 0x0, 0x9c, 0xff, 0xca, 0x0, 0x4,
    0xeb, 0xa0,

    /* U+0053 "S" */
    0x0, 0x6c, 0xcb, 0xb7, 0x0, 0xa, 0xc2, 0x0,
    0xc, 0xa0, 0x4f, 0x20, 0x0, 0x8, 0xa0, 0x8f,
    0x0, 0x0, 0x5, 0x80, 0x7f, 0x30, 0x0, 0x0,
    0x0, 0x2f, 0xe3, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xb4, 0x0, 0x0, 0x0, 0x3b, 0xff, 0xc3, 0x0,
    0x0, 0x0, 0x29, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x2e, 0xf1, 0x10, 0x0, 0x0, 0x6, 0xf4, 0xd1,
    0x0, 0x0, 0x4, 0xf3, 0xe5, 0x0, 0x0, 0x7,
    0xf0, 0xea, 0x0, 0x0, 0x5f, 0x50, 0x7, 0xbb,
    0xcc, 0xa2, 0x0,

    /* U+0054 "T" */
    0x6f, 0xbb, 0xbd, 0xfd, 0xbb, 0xbe, 0x97, 0xb0,
    0x0, 0x4f, 0x70, 0x0, 0x8a, 0x88, 0x0, 0x4,
    0xf7, 0x0, 0x5, 0xb8, 0x50, 0x0, 0x4f, 0x70,
    0x0, 0x2a, 0x0, 0x0, 0x4, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0x70, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0x70, 0x0, 0x0, 0x0, 0x6, 0xbd, 0xfe, 0xb8,
    0x0, 0x0,

    /* U+0055 "U" */
    0xa, 0xcf, 0xeb, 0x80, 0x0, 0xab, 0xed, 0xa1,
    0x0, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x92, 0x0,
    0x0, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x92, 0x0,
    0x0, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x92, 0x0,
    0x0, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x92, 0x0,
    0x0, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x92, 0x0,
    0x0, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x92, 0x0,
    0x0, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x92, 0x0,
    0x0, 0x2f, 0x90, 0x0, 0x0, 0x0, 0x92, 0x0,
    0x0, 0x1f, 0xa0, 0x0, 0x0, 0x0, 0xb1, 0x0,
    0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0xb0, 0x0,
    0x0, 0xd, 0xf1, 0x0, 0x0, 0x1, 0xc0, 0x0,
    0x0, 0x6, 0xf8, 0x0, 0x0, 0x9, 0x50, 0x0,
    0x0, 0x0, 0xbf, 0x94, 0x24, 0xab, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xce, 0xec, 0x60, 0x0, 0x0,

    /* U+0056 "V" */
    0x9c, 0xff, 0xb9, 0x0, 0x8, 0xbe, 0xda, 0x10,
    0xe, 0xe0, 0x0, 0x0, 0x0, 0xc1, 0x0, 0x0,
    0x9f, 0x40, 0x0, 0x0, 0x1b, 0x0, 0x0, 0x3,
    0xf9, 0x0, 0x0, 0x6, 0x60, 0x0, 0x0, 0xe,
    0xe0, 0x0, 0x0, 0xb1, 0x0, 0x0, 0x0, 0x8f,
    0x30, 0x0, 0x1b, 0x0, 0x0, 0x0, 0x3, 0xf8,
    0x0, 0x6, 0x60, 0x0, 0x0, 0x0, 0xd, 0xe0,
    0x0, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x30,
    0xb, 0x0, 0x0, 0x0, 0x0, 0x2, 0xf8, 0x5,
    0x70, 0x0, 0x0, 0x0, 0x0, 0xd, 0xd0, 0xa1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x3b, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xfc, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7c, 0x0, 0x0, 0x0,
    0x0,

    /* U+0057 "W" */
    0x7b, 0xff, 0xb9, 0x0, 0x7b, 0xfe, 0xb8, 0x0,
    0xac, 0xfc, 0x90, 0xc, 0xf0, 0x0, 0x0, 0xe,
    0xd0, 0x0, 0x0, 0x1b, 0x0, 0x0, 0x8f, 0x40,
    0x0, 0x3, 0xef, 0x20, 0x0, 0x5, 0x70, 0x0,
    0x3, 0xf8, 0x0, 0x0, 0x76, 0xf6, 0x0, 0x0,
    0x93, 0x0, 0x0, 0xf, 0xc0, 0x0, 0xb, 0xe,
    0xb0, 0x0, 0xc, 0x0, 0x0, 0x0, 0xaf, 0x10,
    0x1, 0x90, 0x9f, 0x0, 0x2, 0xa0, 0x0, 0x0,
    0x6, 0xf5, 0x0, 0x55, 0x5, 0xf4, 0x0, 0x66,
    0x0, 0x0, 0x0, 0x2f, 0x90, 0xa, 0x10, 0xf,
    0x90, 0xa, 0x20, 0x0, 0x0, 0x0, 0xdd, 0x0,
    0xa0, 0x0, 0xbe, 0x0, 0xc0, 0x0, 0x0, 0x0,
    0x9, 0xf1, 0x37, 0x0, 0x7, 0xf2, 0x29, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x58, 0x20, 0x0, 0x2f,
    0x76, 0x50, 0x0, 0x0, 0x0, 0x0, 0xfa, 0xb0,
    0x0, 0x0, 0xdc, 0xa1, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xe9, 0x0, 0x0, 0x8, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0x40, 0x0, 0x0, 0x4f,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x3, 0xf0, 0x0,
    0x0, 0x0, 0xf4, 0x0, 0x0, 0x0,

    /* U+0058 "X" */
    0x4b, 0xff, 0xdb, 0x30, 0x5b, 0xed, 0xb3, 0x0,
    0x5f, 0x90, 0x0, 0x0, 0xc1, 0x0, 0x0, 0xc,
    0xf2, 0x0, 0x6, 0x60, 0x0, 0x0, 0x3, 0xfb,
    0x0, 0x1b, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x40,
    0x92, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xd3, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xbf, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xb, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x84, 0x2, 0xfc, 0x0, 0x0, 0x0, 0x2, 0xa0,
    0x0, 0x9f, 0x50, 0x0, 0x0, 0xb, 0x10, 0x0,
    0x1e, 0xe0, 0x0, 0x0, 0x67, 0x0, 0x0, 0x6,
    0xf7, 0x0, 0x7c, 0xfd, 0xb1, 0x1, 0xac, 0xff,
    0xb7,

    /* U+0059 "Y" */
    0x4b, 0xff, 0xca, 0x10, 0x3b, 0xde, 0xb4, 0x0,
    0xaf, 0x40, 0x0, 0x0, 0x84, 0x0, 0x0, 0x2f,
    0xc0, 0x0, 0x0, 0xc0, 0x0, 0x0, 0xa, 0xf3,
    0x0, 0x7, 0x50, 0x0, 0x0, 0x3, 0xfb, 0x0,
    0xb, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x20, 0x66,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xa0, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xf6, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xac, 0xff, 0xba, 0x10,
    0x0,

    /* U+005A "Z" */
    0xf, 0xcb, 0xbb, 0xbb, 0xbf, 0xf2, 0xf, 0x20,
    0x0, 0x0, 0x4f, 0xa0, 0x1f, 0x0, 0x0, 0x0,
    0xde, 0x10, 0x2b, 0x0, 0x0, 0x8, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0x20, 0x0, 0x0, 0x0, 0x7, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0x30, 0x0, 0x0, 0x0, 0x6,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x40, 0x0, 0x0, 0x67,
    0x4, 0xfa, 0x0, 0x0, 0x0, 0x97, 0xe, 0xe1,
    0x0, 0x0, 0x0, 0xd6, 0x6f, 0xeb, 0xbb, 0xbb,
    0xbb, 0xf6,

    /* U+005B "[" */
    0xdf, 0xcb, 0xd, 0x90, 0x0, 0xd9, 0x0, 0xd,
    0x90, 0x0, 0xc9, 0x0, 0xc, 0x90, 0x0, 0xc9,
    0x0, 0xc, 0x80, 0x0, 0xc8, 0x0, 0xc, 0x80,
    0x0, 0xc8, 0x0, 0xc, 0x80, 0x0, 0xc9, 0x0,
    0xc, 0x90, 0x0, 0xc9, 0x0, 0xd, 0x90, 0x0,
    0xd9, 0x0, 0xd, 0x90, 0x0, 0xdc, 0x43, 0x6,
    0x77, 0x70,

    /* U+005C "\\" */
    0xb3, 0x0, 0x0, 0x6, 0x80, 0x0, 0x0, 0x1c,
    0x0, 0x0, 0x0, 0xc1, 0x0, 0x0, 0x8, 0x60,
    0x0, 0x0, 0x3b, 0x0, 0x0, 0x0, 0xd0, 0x0,
    0x0, 0x9, 0x40, 0x0, 0x0, 0x59, 0x0, 0x0,
    0x0, 0xd0, 0x0, 0x0, 0xb, 0x30, 0x0, 0x0,
    0x67, 0x0, 0x0, 0x2, 0xc0, 0x0, 0x0, 0xd,
    0x10, 0x0, 0x0, 0x86, 0x0, 0x0, 0x3, 0xa0,
    0x0, 0x0, 0xe, 0x0, 0x0, 0x0, 0xa4, 0x0,
    0x0, 0x5, 0x90, 0x0, 0x0, 0x6,

    /* U+005D "]" */
    0x1b, 0xcf, 0xb0, 0x0, 0xbb, 0x0, 0xb, 0xb0,
    0x0, 0xba, 0x0, 0xb, 0xa0, 0x0, 0xba, 0x0,
    0xb, 0xa0, 0x0, 0xba, 0x0, 0xb, 0xa0, 0x0,
    0xba, 0x0, 0xb, 0xa0, 0x0, 0xba, 0x0, 0xb,
    0xa0, 0x0, 0xba, 0x0, 0xb, 0xa0, 0x0, 0xba,
    0x0, 0xb, 0xb0, 0x0, 0xbb, 0x3, 0x4d, 0xb1,
    0x77, 0x75,

    /* U+005E "^" */
    0x0, 0x0, 0xd8, 0x0, 0x0, 0x0, 0x78, 0xd2,
    0x0, 0x0, 0x1d, 0x4, 0xb0, 0x0, 0xa, 0x40,
    0xa, 0x50, 0x4, 0xa0, 0x0, 0x1d, 0x0, 0xc1,
    0x0, 0x0, 0x69, 0x3, 0x0, 0x0, 0x0, 0x30,

    /* U+005F "_" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,

    /* U+0060 "`" */
    0xe, 0x50, 0x0, 0xae, 0x10, 0x0, 0xaa, 0x0,
    0x0, 0x94, 0x0, 0x0, 0x10,

    /* U+0061 "a" */
    0x2, 0x99, 0xac, 0x70, 0x0, 0x2e, 0x50, 0x2,
    0xf6, 0x0, 0x9f, 0x10, 0x0, 0xdb, 0x0, 0x13,
    0x0, 0x0, 0xbd, 0x0, 0x0, 0x0, 0x37, 0xdd,
    0x0, 0x1, 0x8a, 0x51, 0xbd, 0x0, 0x3e, 0x60,
    0x0, 0xbd, 0x0, 0xcc, 0x0, 0x0, 0xbd, 0x0,
    0xeb, 0x0, 0x1, 0xde, 0x0, 0xaf, 0x51, 0x49,
    0x8f, 0x12, 0x1b, 0xfd, 0x80, 0x1d, 0xd6,

    /* U+0062 "b" */
    0x4, 0x7a, 0x20, 0x0, 0x0, 0x0, 0x17, 0xbf,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x25, 0xbf,
    0xc6, 0x0, 0x0, 0x6f, 0xa5, 0x1, 0x8f, 0x80,
    0x0, 0x6f, 0x30, 0x0, 0xa, 0xf2, 0x0, 0x6f,
    0x30, 0x0, 0x3, 0xf8, 0x0, 0x6f, 0x30, 0x0,
    0x1, 0xfa, 0x0, 0x6f, 0x30, 0x0, 0x0, 0xfc,
    0x0, 0x7f, 0x30, 0x0, 0x1, 0xfa, 0x0, 0x7f,
    0x30, 0x0, 0x3, 0xf7, 0x0, 0x7f, 0x30, 0x0,
    0xa, 0xf1, 0x0, 0x7f, 0xa5, 0x11, 0x8f, 0x60,
    0x2f, 0xff, 0x6, 0xcf, 0xb5, 0x0,

    /* U+0063 "c" */
    0x0, 0x2, 0xab, 0xaa, 0x40, 0x0, 0x6e, 0x50,
    0x3, 0xf4, 0x2, 0xf7, 0x0, 0x0, 0xdc, 0xa,
    0xf0, 0x0, 0x0, 0x23, 0xd, 0xd0, 0x0, 0x0,
    0x0, 0xf, 0xb0, 0x0, 0x0, 0x0, 0xe, 0xd0,
    0x0, 0x0, 0x0, 0xb, 0xf1, 0x0, 0x0, 0x0,
    0x5, 0xf9, 0x0, 0x0, 0x3, 0x0, 0xaf, 0xa3,
    0x13, 0x94, 0x0, 0x5, 0xbe, 0xeb, 0x30,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x4, 0x7a, 0x20, 0x0, 0x0,
    0x0, 0x1, 0x7b, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0x30, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xf2, 0x0, 0x0,
    0x5, 0xcf, 0xc5, 0x6f, 0x20, 0x0, 0x9, 0xf7,
    0x11, 0x6c, 0xf2, 0x0, 0x3, 0xf7, 0x0, 0x0,
    0x7f, 0x20, 0x0, 0xbf, 0x0, 0x0, 0x6, 0xf2,
    0x0, 0xe, 0xd0, 0x0, 0x0, 0x6f, 0x20, 0x0,
    0xfc, 0x0, 0x0, 0x6, 0xf2, 0x0, 0xe, 0xc0,
    0x0, 0x0, 0x6f, 0x20, 0x0, 0xce, 0x0, 0x0,
    0x6, 0xf2, 0x0, 0x5, 0xf5, 0x0, 0x0, 0x7f,
    0x20, 0x0, 0xb, 0xe2, 0x0, 0x3c, 0xf2, 0x0,
    0x0, 0x7, 0xcc, 0xa5, 0x6f, 0xcb, 0x0,

    /* U+0065 "e" */
    0x0, 0x4, 0xaa, 0xab, 0x30, 0x0, 0x7, 0xc1,
    0x0, 0x4f, 0x30, 0x3, 0xf2, 0x0, 0x0, 0xbc,
    0x0, 0xbd, 0x0, 0x0, 0xa, 0xf0, 0xd, 0xe9,
    0x99, 0x9a, 0xcc, 0x0, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0x20, 0x0, 0x0, 0x0, 0x4, 0xfb, 0x0, 0x0,
    0x6, 0x0, 0x9, 0xfb, 0x31, 0x49, 0x60, 0x0,
    0x5, 0xbe, 0xfc, 0x40, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x8, 0xbd, 0x70, 0x0, 0x0, 0xd4,
    0x2c, 0xd0, 0x0, 0x8, 0xb0, 0x0, 0x0, 0x0,
    0xf, 0x50, 0x0, 0x0, 0x0, 0x4f, 0x30, 0x0,
    0x0, 0x0, 0x6f, 0x30, 0x0, 0x0, 0x3b, 0xdf,
    0xdc, 0xc3, 0x0, 0x0, 0x6f, 0x30, 0x0, 0x0,
    0x0, 0x6f, 0x30, 0x0, 0x0, 0x0, 0x6f, 0x30,
    0x0, 0x0, 0x0, 0x6f, 0x30, 0x0, 0x0, 0x0,
    0x6f, 0x30, 0x0, 0x0, 0x0, 0x7f, 0x30, 0x0,
    0x0, 0x0, 0x7f, 0x30, 0x0, 0x0, 0x0, 0x7f,
    0x30, 0x0, 0x0, 0x0, 0x7f, 0x40, 0x0, 0x0,
    0x2a, 0xef, 0xda, 0x20, 0x0,

    /* U+0067 "g" */
    0x0, 0x2a, 0xaa, 0xb5, 0x59, 0xa0, 0x1e, 0x80,
    0x1, 0xeb, 0x43, 0x8, 0xf0, 0x0, 0x8, 0xf0,
    0x0, 0xae, 0x0, 0x0, 0x6f, 0x10, 0x6, 0xf1,
    0x0, 0x9, 0xe0, 0x0, 0xc, 0xa0, 0x3, 0xf5,
    0x0, 0x0, 0x49, 0x99, 0x93, 0x0, 0x0, 0x1b,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xb8, 0x77, 0x75, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xfd, 0x10, 0x4a, 0x0, 0x0,
    0x16, 0xf8, 0xf, 0x30, 0x0, 0x0, 0xd, 0x92,
    0xf2, 0x0, 0x0, 0x0, 0xf4, 0xd, 0xb1, 0x0,
    0x3, 0xc7, 0x0, 0x18, 0xcc, 0xbb, 0x92, 0x0,

    /* U+0068 "h" */
    0x3, 0x6a, 0x20, 0x0, 0x0, 0x0, 0x1, 0x7b,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x21, 0x9e, 0xea, 0x0, 0x0, 0x6, 0xf5,
    0xb6, 0x27, 0xf8, 0x0, 0x0, 0x6f, 0x90, 0x0,
    0xd, 0xd0, 0x0, 0x6, 0xf3, 0x0, 0x0, 0xaf,
    0x0, 0x0, 0x6f, 0x30, 0x0, 0xa, 0xf0, 0x0,
    0x6, 0xf3, 0x0, 0x0, 0xaf, 0x0, 0x0, 0x7f,
    0x30, 0x0, 0xa, 0xf0, 0x0, 0x7, 0xf3, 0x0,
    0x0, 0xaf, 0x0, 0x0, 0x7f, 0x30, 0x0, 0xa,
    0xf0, 0x0, 0x7, 0xf3, 0x0, 0x0, 0xaf, 0x0,
    0x1a, 0xdf, 0xc7, 0x2, 0xae, 0xfb, 0x50,

    /* U+0069 "i" */
    0x0, 0x8d, 0x20, 0x0, 0xcf, 0x40, 0x0, 0x13,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0x9e, 0x40, 0x16, 0xaf, 0x30, 0x0, 0x7f, 0x30,
    0x0, 0x6f, 0x30, 0x0, 0x6f, 0x30, 0x0, 0x6f,
    0x30, 0x0, 0x6f, 0x30, 0x0, 0x6f, 0x30, 0x0,
    0x6f, 0x30, 0x0, 0x7f, 0x30, 0x1a, 0xdf, 0xc7,

    /* U+006A "j" */
    0x0, 0x0, 0x9d, 0x10, 0x0, 0xd, 0xf3, 0x0,
    0x0, 0x13, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x8c, 0xf2, 0x0, 0x13, 0xaf,
    0x20, 0x0, 0x8, 0xf2, 0x0, 0x0, 0x8f, 0x10,
    0x0, 0x8, 0xf1, 0x0, 0x0, 0x7f, 0x10, 0x0,
    0x7, 0xf1, 0x0, 0x0, 0x7f, 0x10, 0x0, 0x8,
    0xf1, 0x0, 0x0, 0x8f, 0x10, 0x0, 0x8, 0xf1,
    0x0, 0x0, 0x8f, 0x0, 0x0, 0x9, 0xe0, 0x0,
    0x0, 0xc9, 0x3, 0xb6, 0x2e, 0x10, 0x3d, 0xea,
    0x10, 0x0,

    /* U+006B "k" */
    0x3, 0x6a, 0x20, 0x0, 0x0, 0x0, 0x1, 0x7b,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x30, 0x3b, 0xee, 0xb5, 0x0, 0x6, 0xf3,
    0x0, 0x2b, 0x10, 0x0, 0x0, 0x6f, 0x30, 0x1b,
    0x10, 0x0, 0x0, 0x6, 0xf3, 0x1c, 0x10, 0x0,
    0x0, 0x0, 0x6f, 0x3b, 0xf5, 0x0, 0x0, 0x0,
    0x6, 0xfc, 0x2d, 0xd0, 0x0, 0x0, 0x0, 0x7f,
    0x40, 0x5f, 0x70, 0x0, 0x0, 0x7, 0xf3, 0x0,
    0xce, 0x10, 0x0, 0x0, 0x7f, 0x30, 0x4, 0xf8,
    0x0, 0x0, 0x7, 0xf3, 0x0, 0xb, 0xf1, 0x0,
    0x1a, 0xdf, 0xc8, 0x0, 0x2f, 0xda, 0x0,

    /* U+006C "l" */
    0x3, 0x6a, 0x20, 0x1, 0x7b, 0xf3, 0x0, 0x0,
    0x6f, 0x30, 0x0, 0x6, 0xf3, 0x0, 0x0, 0x6f,
    0x30, 0x0, 0x6, 0xf3, 0x0, 0x0, 0x6f, 0x30,
    0x0, 0x6, 0xf3, 0x0, 0x0, 0x6f, 0x30, 0x0,
    0x6, 0xf3, 0x0, 0x0, 0x6f, 0x30, 0x0, 0x6,
    0xf3, 0x0, 0x0, 0x7f, 0x30, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0x7f, 0x30, 0x0, 0x7, 0xf3, 0x0,
    0x2a, 0xdf, 0xca, 0x0,

    /* U+006D "m" */
    0x5, 0x9d, 0x11, 0xae, 0xe7, 0x0, 0x7d, 0xfc,
    0x20, 0x2, 0x7b, 0xf5, 0xb5, 0x2a, 0xf4, 0xa7,
    0x34, 0xec, 0x0, 0x0, 0x7f, 0x90, 0x0, 0x1f,
    0xd2, 0x0, 0x8, 0xf1, 0x0, 0x7, 0xf2, 0x0,
    0x0, 0xfa, 0x0, 0x0, 0x6f, 0x30, 0x0, 0x7f,
    0x20, 0x0, 0xe, 0xb0, 0x0, 0x5, 0xf4, 0x0,
    0x7, 0xf2, 0x0, 0x0, 0xeb, 0x0, 0x0, 0x5f,
    0x40, 0x0, 0x7f, 0x30, 0x0, 0xe, 0xb0, 0x0,
    0x5, 0xf4, 0x0, 0x7, 0xf3, 0x0, 0x0, 0xeb,
    0x0, 0x0, 0x5f, 0x40, 0x0, 0x7f, 0x30, 0x0,
    0xe, 0xb0, 0x0, 0x6, 0xf4, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0xeb, 0x0, 0x0, 0x6f, 0x40, 0x1a,
    0xdf, 0xc7, 0x5, 0xbf, 0xea, 0x20, 0xad, 0xfc,
    0x80,

    /* U+006E "n" */
    0x5, 0x9d, 0x11, 0x9e, 0xe9, 0x0, 0x2, 0x7b,
    0xf4, 0xb6, 0x27, 0xf7, 0x0, 0x0, 0x7f, 0x90,
    0x0, 0xd, 0xc0, 0x0, 0x7, 0xf2, 0x0, 0x0,
    0xbe, 0x0, 0x0, 0x7f, 0x20, 0x0, 0xa, 0xf0,
    0x0, 0x7, 0xf2, 0x0, 0x0, 0xaf, 0x0, 0x0,
    0x7f, 0x30, 0x0, 0xa, 0xf0, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0xaf, 0x0, 0x0, 0x7f, 0x30, 0x0,
    0xa, 0xf0, 0x0, 0x7, 0xf3, 0x0, 0x0, 0xbf,
    0x0, 0x1a, 0xdf, 0xc7, 0x3, 0xae, 0xfb, 0x50,

    /* U+006F "o" */
    0x0, 0x4, 0xbb, 0xba, 0x30, 0x0, 0x8, 0xe2,
    0x0, 0x3e, 0x60, 0x3, 0xf5, 0x0, 0x0, 0x7f,
    0x20, 0xbf, 0x0, 0x0, 0x1, 0xf9, 0xd, 0xd0,
    0x0, 0x0, 0xf, 0xc0, 0xfc, 0x0, 0x0, 0x0,
    0xee, 0xe, 0xd0, 0x0, 0x0, 0xf, 0xc0, 0xbf,
    0x0, 0x0, 0x1, 0xf9, 0x4, 0xf5, 0x0, 0x0,
    0x7f, 0x20, 0x8, 0xd2, 0x0, 0x3e, 0x70, 0x0,
    0x4, 0xba, 0xab, 0x30, 0x0,

    /* U+0070 "p" */
    0x5, 0x9d, 0x16, 0xbf, 0xc6, 0x0, 0x27, 0xbf,
    0xa5, 0x1, 0x8f, 0x80, 0x0, 0x7f, 0x40, 0x0,
    0xa, 0xf2, 0x0, 0x7f, 0x30, 0x0, 0x3, 0xf8,
    0x0, 0x7f, 0x30, 0x0, 0x1, 0xfa, 0x0, 0x6f,
    0x30, 0x0, 0x0, 0xfb, 0x0, 0x6f, 0x30, 0x0,
    0x1, 0xf9, 0x0, 0x6f, 0x30, 0x0, 0x4, 0xf7,
    0x0, 0x6f, 0x30, 0x0, 0xb, 0xf1, 0x0, 0x6f,
    0xb4, 0x11, 0x9f, 0x60, 0x0, 0x6f, 0x38, 0xdf,
    0xb4, 0x0, 0x0, 0x7f, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0x30, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x30, 0x0,
    0x0, 0x0, 0x1a, 0xdf, 0xca, 0x10, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x5, 0xce, 0xc6, 0x5, 0x30, 0x0, 0x9f,
    0x61, 0x16, 0x9f, 0x30, 0x3, 0xf7, 0x0, 0x0,
    0x7f, 0x30, 0xb, 0xf0, 0x0, 0x0, 0x6f, 0x30,
    0xd, 0xd0, 0x0, 0x0, 0x6f, 0x30, 0xf, 0xb0,
    0x0, 0x0, 0x6f, 0x30, 0xe, 0xd0, 0x0, 0x0,
    0x6f, 0x30, 0xc, 0xf0, 0x0, 0x0, 0x6f, 0x30,
    0x6, 0xf6, 0x0, 0x0, 0x7f, 0x30, 0x0, 0xcf,
    0x61, 0x17, 0xbf, 0x30, 0x0, 0x8, 0xde, 0xb4,
    0x5f, 0x30, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0x30, 0x0, 0x0, 0x0, 0x3a, 0xdf, 0xc9,

    /* U+0072 "r" */
    0x5, 0x9d, 0x2, 0xbf, 0xa0, 0x27, 0xbf, 0x3c,
    0x5e, 0xe0, 0x0, 0x7f, 0xa2, 0x0, 0x0, 0x0,
    0x7f, 0x90, 0x0, 0x0, 0x0, 0x7f, 0x30, 0x0,
    0x0, 0x0, 0x7f, 0x20, 0x0, 0x0, 0x0, 0x7f,
    0x30, 0x0, 0x0, 0x0, 0x7f, 0x30, 0x0, 0x0,
    0x0, 0x7f, 0x30, 0x0, 0x0, 0x0, 0x7f, 0x30,
    0x0, 0x0, 0x1a, 0xdf, 0xca, 0x10, 0x0,

    /* U+0073 "s" */
    0x4, 0xba, 0xab, 0x50, 0x4e, 0x20, 0x2, 0xf0,
    0xab, 0x0, 0x0, 0xe0, 0xae, 0x20, 0x0, 0x10,
    0x4f, 0xf9, 0x30, 0x0, 0x4, 0xcf, 0xfc, 0x30,
    0x0, 0x2, 0x9f, 0xf2, 0x30, 0x0, 0x3, 0xf7,
    0xe1, 0x0, 0x0, 0xf6, 0xe4, 0x0, 0x6, 0xe1,
    0x5b, 0xba, 0xb9, 0x10,

    /* U+0074 "t" */
    0x0, 0x3f, 0x0, 0x0, 0x0, 0x6f, 0x0, 0x0,
    0x0, 0x9e, 0x0, 0x0, 0x7b, 0xef, 0xcc, 0x90,
    0x0, 0xbe, 0x0, 0x0, 0x0, 0xbe, 0x0, 0x0,
    0x0, 0xbe, 0x0, 0x0, 0x0, 0xbe, 0x0, 0x0,
    0x0, 0xbe, 0x0, 0x0, 0x0, 0xbe, 0x0, 0x0,
    0x0, 0xbe, 0x0, 0x0, 0x0, 0xbf, 0x0, 0x0,
    0x0, 0x8f, 0x40, 0x30, 0x0, 0x1b, 0xfc, 0x60,

    /* U+0075 "u" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x10, 0x3, 0xbf,
    0xf0, 0x0, 0x5c, 0xfb, 0x0, 0x0, 0xbf, 0x0,
    0x0, 0xe, 0xb0, 0x0, 0xb, 0xe0, 0x0, 0x0,
    0xeb, 0x0, 0x0, 0xbe, 0x0, 0x0, 0xe, 0xa0,
    0x0, 0xb, 0xe0, 0x0, 0x0, 0xea, 0x0, 0x0,
    0xbe, 0x0, 0x0, 0xe, 0xa0, 0x0, 0xb, 0xe0,
    0x0, 0x0, 0xea, 0x0, 0x0, 0xaf, 0x0, 0x0,
    0xe, 0xa0, 0x0, 0x9, 0xf2, 0x0, 0x6, 0xfa,
    0x0, 0x0, 0x3f, 0xc3, 0x5a, 0x5e, 0xa0, 0x0,
    0x0, 0x7e, 0xfc, 0x40, 0xef, 0xe2,

    /* U+0076 "v" */
    0x1a, 0xff, 0xb8, 0x0, 0xad, 0xd9, 0x0, 0x8f,
    0x40, 0x0, 0x8, 0x30, 0x0, 0x2f, 0x90, 0x0,
    0xb, 0x0, 0x0, 0xc, 0xf0, 0x0, 0x28, 0x0,
    0x0, 0x6, 0xf5, 0x0, 0x82, 0x0, 0x0, 0x0,
    0xfa, 0x0, 0xa0, 0x0, 0x0, 0x0, 0xaf, 0x12,
    0x70, 0x0, 0x0, 0x0, 0x4f, 0x68, 0x10, 0x0,
    0x0, 0x0, 0xe, 0xb9, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x2, 0xf1,
    0x0, 0x0,

    /* U+0077 "w" */
    0xa, 0xef, 0xb7, 0x4, 0xbf, 0xda, 0x3, 0xbe,
    0xc6, 0x0, 0x8f, 0x20, 0x0, 0x1f, 0xa0, 0x0,
    0xb, 0x10, 0x0, 0x3f, 0x70, 0x0, 0x6b, 0xf0,
    0x0, 0xb, 0x0, 0x0, 0xe, 0xc0, 0x0, 0xa2,
    0xf5, 0x0, 0x46, 0x0, 0x0, 0x8, 0xf1, 0x1,
    0x80, 0xda, 0x0, 0x91, 0x0, 0x0, 0x3, 0xf5,
    0x6, 0x30, 0x7f, 0x0, 0xa0, 0x0, 0x0, 0x0,
    0xea, 0xa, 0x0, 0x2f, 0x42, 0x70, 0x0, 0x0,
    0x0, 0x8f, 0x28, 0x0, 0xd, 0xa7, 0x20, 0x0,
    0x0, 0x0, 0x3f, 0xb2, 0x0, 0x8, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xd0, 0x0, 0x3, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x9, 0x80, 0x0, 0x0,
    0xd3, 0x0, 0x0,

    /* U+0078 "x" */
    0x6c, 0xfe, 0xa0, 0xa, 0xed, 0x80, 0x0, 0xce,
    0x10, 0x0, 0xb1, 0x0, 0x0, 0x3f, 0xb0, 0x8,
    0x40, 0x0, 0x0, 0x8, 0xf5, 0x38, 0x0, 0x0,
    0x0, 0x0, 0xde, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xaa, 0xf4,
    0x0, 0x0, 0x0, 0x6, 0x51, 0xee, 0x0, 0x0,
    0x0, 0x2a, 0x0, 0x5f, 0x90, 0x0, 0x0, 0xb1,
    0x0, 0xa, 0xf3, 0x0, 0x7c, 0xea, 0x10, 0x8c,
    0xfe, 0xa0,

    /* U+0079 "y" */
    0x1a, 0xff, 0xb7, 0x0, 0x9b, 0xea, 0x0, 0x8,
    0xf4, 0x0, 0x0, 0x56, 0x0, 0x0, 0x2f, 0x90,
    0x0, 0xa, 0x10, 0x0, 0x0, 0xbf, 0x0, 0x1,
    0xa0, 0x0, 0x0, 0x5, 0xf5, 0x0, 0x64, 0x0,
    0x0, 0x0, 0xe, 0xb0, 0xa, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x11, 0x80, 0x0, 0x0, 0x0, 0x2,
    0xf6, 0x72, 0x0, 0x0, 0x0, 0x0, 0xb, 0xc9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa0,
    0x0, 0x0, 0x0, 0xd, 0xc3, 0xb2, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xc3, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0xf, 0xba, 0xaa, 0xaf, 0xf0, 0xf, 0x10, 0x0,
    0x5f, 0x60, 0xd, 0x0, 0x1, 0xec, 0x0, 0x2,
    0x0, 0xa, 0xf2, 0x0, 0x0, 0x0, 0x4f, 0x70,
    0x0, 0x0, 0x0, 0xed, 0x0, 0x0, 0x0, 0x9,
    0xf3, 0x0, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x42,
    0x0, 0xdd, 0x0, 0x0, 0xb4, 0x8, 0xf3, 0x0,
    0x0, 0xe3, 0x1f, 0xea, 0xaa, 0xaa, 0xf2,

    /* U+007B "{" */
    0x0, 0x4, 0xcc, 0x90, 0x3, 0xf4, 0x0, 0x0,
    0x7d, 0x0, 0x0, 0x7, 0xe0, 0x0, 0x0, 0x4f,
    0x10, 0x0, 0x0, 0xf3, 0x0, 0x0, 0xa, 0x60,
    0x0, 0x0, 0x88, 0x0, 0x0, 0xc, 0x30, 0x0,
    0x8a, 0x50, 0x0, 0x6, 0xa7, 0x0, 0x0, 0x0,
    0xb4, 0x0, 0x0, 0x8, 0x80, 0x0, 0x0, 0xb6,
    0x0, 0x0, 0xf, 0x30, 0x0, 0x4, 0xf0, 0x0,
    0x0, 0x7e, 0x0, 0x0, 0x7, 0xd0, 0x0, 0x0,
    0x2f, 0x60, 0x0, 0x0, 0x29, 0xb8,

    /* U+007C "|" */
    0x25, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a,
    0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a,
    0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a, 0x5a,

    /* U+007D "}" */
    0x3b, 0xd9, 0x10, 0x0, 0x0, 0xca, 0x0, 0x0,
    0x6, 0xf0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x9,
    0xb0, 0x0, 0x0, 0xc7, 0x0, 0x0, 0xe, 0x20,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0xc, 0x40, 0x0,
    0x0, 0x1a, 0x94, 0x0, 0x2, 0xb8, 0x30, 0x0,
    0xd3, 0x0, 0x0, 0xf, 0x0, 0x0, 0x0, 0xe2,
    0x0, 0x0, 0xb, 0x70, 0x0, 0x0, 0x9c, 0x0,
    0x0, 0x6, 0xf0, 0x0, 0x0, 0x6e, 0x0, 0x0,
    0x1c, 0x90, 0x3, 0xab, 0x60, 0x0,

    /* U+007E "~" */
    0x9, 0xec, 0x40, 0x0, 0x55, 0x88, 0x3, 0xc9,
    0x11, 0xc2, 0xa0, 0x0, 0x7, 0xdd, 0x60,

    /* U+2103 "℃" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9a, 0xa9, 0x0, 0x0, 0x1, 0xac, 0xab,
    0xa2, 0x75, 0x0, 0x57, 0x0, 0x4, 0xe7, 0x0,
    0x9, 0xaa, 0x0, 0x0, 0xa0, 0x0, 0xe9, 0x0,
    0x0, 0x6b, 0x74, 0x0, 0x47, 0x0, 0x6f, 0x30,
    0x0, 0x2, 0x90, 0x9a, 0xa9, 0x0, 0xc, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xeb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xd0, 0x0, 0x0, 0x3, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0x30, 0x0, 0x2, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0xe9, 0x0, 0x0, 0x5d, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf7, 0x0, 0x8, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xbc, 0xab, 0xa3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+2B05 "⬅" */
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x98, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x2, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x1, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0xaf, 0xff, 0xda, 0xaa, 0xaa, 0xaa, 0xaa,
    0xa0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x57,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4E00 "一" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcb, 0x10, 0xa, 0xaa, 0xaa, 0xaa,
    0xaa, 0xaa, 0xaa, 0xac, 0xff, 0xc0,

    /* U+4EAE "亮" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xd7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf2, 0x0, 0x0, 0x28, 0x0, 0x69, 0x99, 0x99,
    0x99, 0x9d, 0x99, 0x99, 0x9d, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x2, 0xe9, 0x88, 0x88, 0x88, 0x9e, 0xb0,
    0x0, 0x0, 0x0, 0x2f, 0x30, 0x0, 0x0, 0x0,
    0xd9, 0x0, 0x0, 0x0, 0x2, 0xf2, 0x0, 0x0,
    0x0, 0xd, 0x70, 0x0, 0x0, 0x0, 0x2f, 0xa9,
    0x99, 0x99, 0x99, 0xf7, 0x0, 0x0, 0x1, 0x11,
    0xa1, 0x0, 0x0, 0x0, 0x7, 0x20, 0x0, 0x0,
    0x55, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d,
    0x10, 0xb, 0xc9, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x9c, 0xf7, 0x9, 0xf3, 0x0, 0x42, 0x0, 0x0,
    0x60, 0x0, 0xa7, 0x0, 0x86, 0x0, 0xa, 0xe9,
    0x99, 0x9f, 0xa0, 0x5, 0x0, 0x0, 0x0, 0x0,
    0xba, 0x0, 0x0, 0xe5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x70, 0x0, 0xe, 0x50, 0x0, 0x20,
    0x0, 0x0, 0x7, 0xe0, 0x0, 0x0, 0xe5, 0x0,
    0x8, 0x0, 0x0, 0x6, 0xe3, 0x0, 0x0, 0xe,
    0x83, 0x33, 0xd1, 0x0, 0x2a, 0x81, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xfd, 0x40, 0x35, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4EEA "仪" */
    0x0, 0x0, 0x2, 0x0, 0x0, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xe3, 0x0, 0x4c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x80,
    0x0, 0xa, 0xb0, 0x0, 0x53, 0x0, 0x0, 0x0,
    0x9f, 0x10, 0x60, 0x2, 0xf3, 0x0, 0xfa, 0x0,
    0x0, 0x0, 0xe9, 0x0, 0x70, 0x0, 0xd2, 0x3,
    0xf3, 0x0, 0x0, 0x5, 0xf2, 0x0, 0x80, 0x0,
    0x0, 0x6, 0xf0, 0x0, 0x0, 0xb, 0xf2, 0x0,
    0x63, 0x0, 0x0, 0xa, 0xc0, 0x0, 0x0, 0x3f,
    0xe0, 0x0, 0x19, 0x0, 0x0, 0xf, 0x60, 0x0,
    0x0, 0xba, 0xe0, 0x0, 0xb, 0x0, 0x0, 0x4f,
    0x10, 0x0, 0x5, 0x65, 0xe0, 0x0, 0x7, 0x60,
    0x0, 0xbb, 0x0, 0x0, 0x7, 0x5, 0xe0, 0x0,
    0x1, 0xd0, 0x1, 0xf3, 0x0, 0x0, 0x0, 0x5,
    0xe0, 0x0, 0x0, 0x99, 0xa, 0xb0, 0x0, 0x0,
    0x0, 0x5, 0xe0, 0x0, 0x0, 0x1e, 0x8f, 0x10,
    0x0, 0x0, 0x0, 0x5, 0xe0, 0x0, 0x0, 0x6,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x0, 0xb, 0xef, 0x40, 0x0, 0x0, 0x0, 0x5,
    0xe0, 0x0, 0x0, 0xb7, 0xb, 0xf6, 0x0, 0x0,
    0x0, 0x5, 0xe0, 0x0, 0xa, 0x50, 0x0, 0xaf,
    0xb2, 0x0, 0x0, 0x5, 0xe0, 0x5, 0xa3, 0x0,
    0x0, 0x7, 0xff, 0xa2, 0x0, 0x5, 0xc1, 0x74,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+4F53 "体" */
    0x0, 0x0, 0x67, 0x20, 0x0, 0x9, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdd, 0x10, 0x0, 0xe,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x2, 0xf6, 0x0,
    0x0, 0xe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf0, 0x0, 0x0, 0xe, 0x50, 0x0, 0x63, 0x0,
    0x0, 0xc, 0x94, 0x99, 0x99, 0x9f, 0xb9, 0x99,
    0xff, 0x20, 0x0, 0x1f, 0x70, 0x0, 0x0, 0xef,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x70, 0x0,
    0x4, 0xff, 0x78, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x60, 0x0, 0x9, 0xbe, 0x5b, 0x0, 0x0, 0x0,
    0x6, 0x6e, 0x60, 0x0, 0xf, 0x4e, 0x56, 0x80,
    0x0, 0x0, 0x9, 0xe, 0x60, 0x0, 0x7c, 0xe,
    0x50, 0xe2, 0x0, 0x0, 0x31, 0xe, 0x60, 0x0,
    0xe3, 0xe, 0x50, 0x8c, 0x0, 0x0, 0x0, 0xe,
    0x60, 0x8, 0x80, 0xe, 0x50, 0xe, 0xa0, 0x0,
    0x0, 0xe, 0x60, 0x2c, 0x0, 0xe, 0x50, 0x5,
    0xf8, 0x0, 0x0, 0xe, 0x60, 0xb1, 0x0, 0xe,
    0x50, 0xb5, 0x9f, 0xb0, 0x0, 0xe, 0x68, 0x11,
    0x99, 0x9f, 0xb9, 0x98, 0xa, 0x40, 0x0, 0xe,
    0x71, 0x0, 0x0, 0xe, 0x50, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x60, 0x0, 0x0, 0xe, 0x50, 0x0,
    0x0, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0, 0xe,
    0x50, 0x0, 0x0, 0x0, 0x0, 0xd, 0x40, 0x0,
    0x0, 0xd, 0x40, 0x0, 0x0, 0x0,

    /* U+4FE1 "信" */
    0x0, 0x0, 0x5, 0x20, 0x0, 0x9, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xb1, 0x0, 0x2,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x20,
    0x0, 0x0, 0x9e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xca, 0x0, 0x0, 0x0, 0x15, 0x0, 0xd, 0x70,
    0x0, 0x2, 0xf3, 0x59, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x91, 0x0, 0x8, 0xe1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x60, 0x0, 0x0, 0xe, 0xf0, 0x1,
    0x99, 0x99, 0x99, 0x9a, 0xfc, 0x0, 0x0, 0x6d,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb6, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x81, 0x0, 0x7, 0x25, 0xf0, 0x1, 0x99, 0x99,
    0x99, 0x9a, 0xeb, 0x0, 0x4, 0x5, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xf0, 0x0, 0x60, 0x0, 0x0, 0x0, 0x82, 0x0,
    0x0, 0x5, 0xf0, 0x0, 0xeb, 0x99, 0x99, 0x99,
    0xfb, 0x0, 0x0, 0x5, 0xf0, 0x0, 0xe5, 0x0,
    0x0, 0x0, 0xe6, 0x0, 0x0, 0x5, 0xf0, 0x0,
    0xe5, 0x0, 0x0, 0x0, 0xe6, 0x0, 0x0, 0x5,
    0xf0, 0x0, 0xe5, 0x0, 0x0, 0x0, 0xe6, 0x0,
    0x0, 0x5, 0xf0, 0x0, 0xeb, 0x99, 0x99, 0x99,
    0xf6, 0x0, 0x0, 0x5, 0xf0, 0x0, 0xe5, 0x0,
    0x0, 0x0, 0xd4, 0x0, 0x0, 0x5, 0xc0, 0x0,
    0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+503C "值" */
    0x0, 0x0, 0x26, 0x10, 0x0, 0x4, 0x83, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x50, 0x0, 0x8,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xea, 0x0,
    0x0, 0x8, 0xc0, 0x0, 0x4e, 0x30, 0x0, 0x4,
    0xf3, 0x79, 0x99, 0x9d, 0xd9, 0x99, 0x99, 0x70,
    0x0, 0xa, 0xb0, 0x0, 0x0, 0x9, 0x90, 0x0,
    0x10, 0x0, 0x0, 0xf, 0x90, 0x4, 0xd9, 0x9d,
    0xd9, 0x9c, 0xf5, 0x0, 0x0, 0x6f, 0x60, 0x4,
    0xf0, 0x0, 0x0, 0x6, 0xe0, 0x0, 0x0, 0xdf,
    0x60, 0x4, 0xf0, 0x0, 0x0, 0x6, 0xd0, 0x0,
    0x7, 0x6e, 0x60, 0x4, 0xf9, 0x99, 0x99, 0x9c,
    0xd0, 0x0, 0x19, 0xe, 0x60, 0x4, 0xf0, 0x0,
    0x0, 0x6, 0xd0, 0x0, 0x30, 0xe, 0x60, 0x4,
    0xf0, 0x0, 0x0, 0x6, 0xd0, 0x0, 0x0, 0xe,
    0x60, 0x4, 0xf9, 0x99, 0x99, 0x9c, 0xd0, 0x0,
    0x0, 0xe, 0x60, 0x4, 0xf0, 0x0, 0x0, 0x6,
    0xd0, 0x0, 0x0, 0xe, 0x60, 0x4, 0xf0, 0x0,
    0x0, 0x6, 0xd0, 0x0, 0x0, 0xe, 0x60, 0x4,
    0xf9, 0x99, 0x99, 0x9c, 0xd0, 0x0, 0x0, 0xe,
    0x60, 0x4, 0xf0, 0x0, 0x0, 0x6, 0xd0, 0x0,
    0x0, 0xe, 0x60, 0x4, 0xf0, 0x0, 0x0, 0x6,
    0xd5, 0x80, 0x0, 0xe, 0x66, 0x9a, 0xb9, 0x99,
    0x99, 0x9a, 0xbb, 0xb3, 0x0, 0xd, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5165 "入" */
    0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7e, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9d, 0x95, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe7, 0x4d, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf1, 0xe,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x90, 0x9, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6e, 0x0, 0x1, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xe5, 0x0, 0x0, 0x8f, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xa0, 0x0, 0x0,
    0x1f, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x7b, 0x0,
    0x0, 0x0, 0x5, 0xfc, 0x0, 0x0, 0x0, 0x4,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xc1, 0x0,
    0x0, 0x4b, 0x10, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xfe, 0x60, 0x5, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xc3, 0x13, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x0,

    /* U+5173 "关" */
    0x0, 0x0, 0x15, 0x0, 0x0, 0x0, 0x6, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xd3, 0x0, 0x0,
    0x1f, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x20, 0x0, 0x8d, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x80, 0x1, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x20, 0x8, 0x30, 0xb,
    0xb0, 0x0, 0x0, 0x69, 0x99, 0x99, 0x9f, 0xc9,
    0x99, 0x99, 0x94, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x50,
    0x0, 0x0, 0x9d, 0x20, 0x9, 0x99, 0x99, 0x99,
    0xaf, 0xd9, 0x99, 0x99, 0x99, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x6e, 0x63, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc9, 0xb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf2, 0x7,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0x50, 0x0, 0xca, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xe5, 0x0, 0x0, 0x1e, 0xe5, 0x0, 0x0,
    0x0, 0x1, 0xab, 0x20, 0x0, 0x0, 0x1, 0xcf,
    0xc7, 0x20, 0x2, 0x89, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xef, 0xb0, 0x25, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0x0,

    /* U+51FA "出" */
    0x0, 0x0, 0x0, 0x0, 0x75, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa8, 0x0, 0x0, 0xf5, 0x0,
    0x0, 0xa8, 0x0, 0x0, 0xe5, 0x0, 0x0, 0xf5,
    0x0, 0x0, 0xe5, 0x0, 0x0, 0xe5, 0x0, 0x0,
    0xf5, 0x0, 0x0, 0xe5, 0x0, 0x0, 0xe5, 0x0,
    0x0, 0xf5, 0x0, 0x0, 0xe5, 0x0, 0x0, 0xe5,
    0x0, 0x0, 0xf5, 0x0, 0x0, 0xe5, 0x0, 0x0,
    0xe5, 0x0, 0x0, 0xf5, 0x0, 0x0, 0xe5, 0x0,
    0x2, 0xec, 0x99, 0x99, 0xfb, 0x99, 0x99, 0xf5,
    0x0, 0x0, 0x10, 0x0, 0x0, 0xf5, 0x0, 0x0,
    0x81, 0x0, 0xa, 0x80, 0x0, 0x0, 0xf5, 0x0,
    0x0, 0x9, 0x70, 0xd, 0x60, 0x0, 0x0, 0xf5,
    0x0, 0x0, 0xe, 0x50, 0xd, 0x60, 0x0, 0x0,
    0xf5, 0x0, 0x0, 0xe, 0x50, 0xd, 0x60, 0x0,
    0x0, 0xf5, 0x0, 0x0, 0xe, 0x50, 0xd, 0x60,
    0x0, 0x0, 0xf5, 0x0, 0x0, 0xe, 0x50, 0xd,
    0x60, 0x0, 0x0, 0xf5, 0x0, 0x0, 0xe, 0x50,
    0x2f, 0xc9, 0x99, 0x99, 0xca, 0x99, 0x99, 0x9f,
    0x50, 0x2, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x30,

    /* U+5206 "分" */
    0x0, 0x0, 0x0, 0x18, 0x20, 0x0, 0xa3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0x50, 0x0,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xea,
    0x0, 0x0, 0x56, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf2, 0x0, 0x0, 0xc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0x60, 0x0, 0x0, 0x7, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0xaa, 0x0, 0x0, 0x0,
    0x0, 0xe7, 0x0, 0x0, 0x0, 0x6, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0x90, 0x0, 0x0, 0x4b,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x46, 0xfd, 0x40,
    0x5, 0x83, 0x99, 0x99, 0xba, 0x99, 0x99, 0xfc,
    0x5e, 0xe3, 0x13, 0x0, 0x0, 0x1, 0xf4, 0x0,
    0x0, 0xe6, 0x1, 0x20, 0x0, 0x0, 0x0, 0x3,
    0xf2, 0x0, 0x0, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xe0, 0x0, 0x0, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0x80, 0x0, 0x2, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x10, 0x0,
    0x3, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb9,
    0x0, 0x0, 0x5, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xc0, 0x0, 0x0, 0x8, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x5c, 0x0, 0x0, 0x0, 0xd, 0x90,
    0x0, 0x0, 0x0, 0x7, 0x80, 0x0, 0x6, 0x9a,
    0xcf, 0x30, 0x0, 0x0, 0x1, 0x83, 0x0, 0x0,
    0x0, 0x8, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+524D "前" */
    0x0, 0x0, 0x7, 0x10, 0x0, 0x0, 0x8, 0x61,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xe4, 0x0, 0x0,
    0x3f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0xae,
    0x0, 0x0, 0xb9, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x3d, 0x0, 0x4, 0x90, 0x0, 0x3f, 0x50,
    0x9, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x80, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x31, 0x0, 0x0, 0x5c, 0x99, 0x9a,
    0xf6, 0x2, 0xc4, 0x0, 0xe8, 0x0, 0x0, 0x5e,
    0x0, 0x3, 0xf2, 0x3, 0xf0, 0x0, 0xe5, 0x0,
    0x0, 0x5e, 0x0, 0x3, 0xf0, 0x3, 0xf0, 0x0,
    0xe5, 0x0, 0x0, 0x5f, 0x99, 0x9a, 0xf0, 0x3,
    0xf0, 0x0, 0xe5, 0x0, 0x0, 0x5e, 0x0, 0x3,
    0xf0, 0x3, 0xf0, 0x0, 0xe5, 0x0, 0x0, 0x5e,
    0x0, 0x3, 0xf0, 0x3, 0xf0, 0x0, 0xe5, 0x0,
    0x0, 0x5e, 0x0, 0x3, 0xf0, 0x3, 0xf0, 0x0,
    0xe5, 0x0, 0x0, 0x5f, 0x99, 0x9a, 0xf0, 0x3,
    0xf0, 0x0, 0xe5, 0x0, 0x0, 0x5e, 0x0, 0x3,
    0xf0, 0x3, 0xf0, 0x0, 0xe5, 0x0, 0x0, 0x5e,
    0x0, 0x3, 0xf0, 0x2, 0x60, 0x0, 0xe5, 0x0,
    0x0, 0x5e, 0x0, 0x3, 0xf0, 0x0, 0x0, 0x0,
    0xe5, 0x0, 0x0, 0x5e, 0x2, 0x8d, 0xf0, 0x0,
    0x5, 0x9c, 0xf4, 0x0, 0x0, 0x5c, 0x0, 0xd,
    0x80, 0x0, 0x0, 0xd, 0xa0, 0x0,

    /* U+5316 "化" */
    0x0, 0x0, 0x1, 0x82, 0x1, 0x53, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xf8, 0x2, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xc0,
    0x2, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0x40, 0x2, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xac, 0x0, 0x2, 0xf2, 0x0, 0x3,
    0xe6, 0x0, 0x0, 0x2, 0xf3, 0x0, 0x2, 0xf2,
    0x0, 0x1e, 0xe4, 0x0, 0x0, 0xb, 0xf3, 0x0,
    0x2, 0xf2, 0x0, 0xce, 0x20, 0x0, 0x0, 0x4f,
    0xf0, 0x0, 0x2, 0xf2, 0xb, 0xe2, 0x0, 0x0,
    0x0, 0xc7, 0xf0, 0x0, 0x2, 0xf3, 0xcc, 0x10,
    0x0, 0x0, 0x9, 0x44, 0xf0, 0x0, 0x2, 0xfe,
    0x90, 0x0, 0x0, 0x0, 0x34, 0x4, 0xf0, 0x0,
    0x4, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf0, 0x0, 0x8c, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf0, 0x29, 0x42, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf2, 0x50, 0x2, 0xf2,
    0x0, 0x0, 0x0, 0x20, 0x0, 0x4, 0xf0, 0x0,
    0x2, 0xf2, 0x0, 0x0, 0x0, 0x70, 0x0, 0x4,
    0xf0, 0x0, 0x2, 0xf2, 0x0, 0x0, 0x2, 0x90,
    0x0, 0x4, 0xf0, 0x0, 0x1, 0xf8, 0x44, 0x44,
    0x4a, 0xc0, 0x0, 0x4, 0xf0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xfe, 0xb1, 0x0, 0x4, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5355 "单" */
    0x0, 0x0, 0x5, 0x0, 0x0, 0x0, 0x3, 0x92,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xd3, 0x0, 0x0,
    0xc, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xae,
    0x10, 0x0, 0x5e, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0x40, 0x1, 0xc2, 0x0, 0x20, 0x0,
    0x0, 0x5, 0xd9, 0x9c, 0x99, 0x9c, 0xa9, 0x9c,
    0xf4, 0x0, 0x0, 0x5, 0xe0, 0x0, 0x6, 0xe0,
    0x0, 0x6, 0xe0, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x6, 0xe0, 0x0, 0x6, 0xe0, 0x0, 0x0, 0x5,
    0xf8, 0x88, 0x8b, 0xf8, 0x88, 0x8b, 0xe0, 0x0,
    0x0, 0x5, 0xe0, 0x0, 0x6, 0xe0, 0x0, 0x7,
    0xe0, 0x0, 0x0, 0x5, 0xe0, 0x0, 0x6, 0xe0,
    0x0, 0x6, 0xe0, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x6, 0xe0, 0x0, 0x6, 0xe0, 0x0, 0x0, 0x5,
    0xf9, 0x99, 0x9c, 0xf9, 0x99, 0x9c, 0xe0, 0x0,
    0x0, 0x3, 0x50, 0x0, 0x6, 0xe0, 0x0, 0x2,
    0x21, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xe0,
    0x0, 0x0, 0xc, 0xc1, 0x9, 0x99, 0x99, 0x99,
    0x9b, 0xf9, 0x99, 0x99, 0x99, 0x95, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xc0, 0x0, 0x0, 0x0, 0x0,

    /* U+5382 "厂" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3c, 0x20, 0x0, 0x1f, 0xa9, 0x99,
    0x99, 0x99, 0x99, 0x99, 0xbc, 0xa0, 0x0, 0x1f,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6c, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x89, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+53ED "叭" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x10, 0x0,
    0xf5, 0x0, 0x0, 0x4d, 0x99, 0xbf, 0x70, 0x3f,
    0x70, 0xa, 0x0, 0x0, 0x4, 0xe0, 0x2, 0xf1,
    0x4, 0xf2, 0x0, 0xa0, 0x0, 0x0, 0x4e, 0x0,
    0x2f, 0x0, 0x4f, 0x10, 0xa, 0x0, 0x0, 0x4,
    0xe0, 0x2, 0xf0, 0x5, 0xf0, 0x0, 0xa0, 0x0,
    0x0, 0x4e, 0x0, 0x2f, 0x0, 0x5e, 0x0, 0x9,
    0x30, 0x0, 0x4, 0xe0, 0x2, 0xf0, 0x7, 0xd0,
    0x0, 0x66, 0x0, 0x0, 0x4e, 0x0, 0x2f, 0x0,
    0xa9, 0x0, 0x4, 0xa0, 0x0, 0x4, 0xe0, 0x2,
    0xf0, 0xd, 0x60, 0x0, 0x2d, 0x0, 0x0, 0x4e,
    0x0, 0x2f, 0x0, 0xf2, 0x0, 0x0, 0xf1, 0x0,
    0x4, 0xe0, 0x2, 0xf0, 0x3e, 0x0, 0x0, 0xc,
    0x70, 0x0, 0x4f, 0x99, 0xaf, 0x9, 0x80, 0x0,
    0x0, 0x7d, 0x0, 0x4, 0xe0, 0x2, 0xf1, 0xe2,
    0x0, 0x0, 0x2, 0xf3, 0x0, 0x4e, 0x0, 0x17,
    0x69, 0x0, 0x0, 0x0, 0xe, 0xa0, 0x3, 0x70,
    0x0, 0xc, 0x10, 0x0, 0x0, 0x0, 0x8f, 0x50,
    0x0, 0x0, 0x9, 0x40, 0x0, 0x0, 0x0, 0x0,
    0xee, 0x10, 0x0, 0x5, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xf5, 0x0, 0x0, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x0,

    /* U+53EF "可" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6e, 0x30, 0x19, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0xcf, 0x99, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e,
    0x0, 0x0, 0x0, 0x5, 0x0, 0x0, 0x8, 0x20,
    0x0, 0x6e, 0x0, 0x0, 0x0, 0xe, 0xb9, 0x99,
    0x9f, 0xd1, 0x0, 0x6e, 0x0, 0x0, 0x0, 0xe,
    0x50, 0x0, 0xe, 0x50, 0x0, 0x6e, 0x0, 0x0,
    0x0, 0xe, 0x50, 0x0, 0xe, 0x50, 0x0, 0x6e,
    0x0, 0x0, 0x0, 0xe, 0x50, 0x0, 0xe, 0x50,
    0x0, 0x6e, 0x0, 0x0, 0x0, 0xe, 0x50, 0x0,
    0xe, 0x50, 0x0, 0x6e, 0x0, 0x0, 0x0, 0xe,
    0x50, 0x0, 0xe, 0x50, 0x0, 0x6e, 0x0, 0x0,
    0x0, 0xe, 0xa8, 0x88, 0x8f, 0x50, 0x0, 0x6e,
    0x0, 0x0, 0x0, 0xe, 0x50, 0x0, 0xe, 0x40,
    0x0, 0x6e, 0x0, 0x0, 0x0, 0xb, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x15,
    0x9e, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0x91, 0x0, 0x0,

    /* U+542F "启" */
    0x0, 0x0, 0x0, 0x0, 0xa, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x1e, 0x90,
    0x0, 0x2, 0x0, 0x0, 0x5, 0xe9, 0x99, 0x99,
    0xde, 0x99, 0x99, 0xf9, 0x0, 0x0, 0x5f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x50, 0x0, 0x5,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf4, 0x0,
    0x0, 0x5f, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x40, 0x0, 0x5, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf4, 0x0, 0x0, 0x6f, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x9f, 0x40, 0x0, 0x6, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x50, 0x0, 0x0, 0x7d,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0,
    0x9, 0xb0, 0xda, 0x99, 0x99, 0x99, 0x9a, 0xf7,
    0x0, 0x0, 0xb8, 0xe, 0x60, 0x0, 0x0, 0x0,
    0x2f, 0x30, 0x0, 0xe, 0x40, 0xe6, 0x0, 0x0,
    0x0, 0x2, 0xf2, 0x0, 0x3, 0xe0, 0xe, 0x60,
    0x0, 0x0, 0x0, 0x2f, 0x20, 0x0, 0x88, 0x0,
    0xe6, 0x0, 0x0, 0x0, 0x2, 0xf2, 0x0, 0xd,
    0x10, 0xe, 0x60, 0x0, 0x0, 0x0, 0x2f, 0x20,
    0x7, 0x50, 0x0, 0xec, 0x99, 0x99, 0x99, 0x9a,
    0xf2, 0x1, 0x70, 0x0, 0xe, 0x50, 0x0, 0x0,
    0x0, 0x2e, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5438 "吸" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x11, 0x0, 0x4, 0x58, 0x88, 0x88,
    0x88, 0xae, 0x30, 0x0, 0x4e, 0x99, 0xbf, 0x70,
    0x4f, 0x10, 0x0, 0xac, 0x10, 0x0, 0x4e, 0x0,
    0x4f, 0x0, 0x4f, 0x0, 0x1, 0xf5, 0x0, 0x0,
    0x4e, 0x0, 0x4e, 0x0, 0x5f, 0x0, 0x7, 0xd0,
    0x0, 0x0, 0x4e, 0x0, 0x4e, 0x0, 0x5f, 0x30,
    0xe, 0x60, 0x0, 0x0, 0x4e, 0x0, 0x4e, 0x0,
    0x6f, 0x70, 0x7e, 0x0, 0x43, 0x0, 0x4e, 0x0,
    0x4e, 0x0, 0x8c, 0xb1, 0xdc, 0x99, 0xfe, 0x10,
    0x4e, 0x0, 0x4e, 0x0, 0xaa, 0xa0, 0x0, 0x2,
    0xf4, 0x0, 0x4e, 0x0, 0x4e, 0x0, 0xc8, 0x65,
    0x0, 0x8, 0xd0, 0x0, 0x4e, 0x0, 0x4e, 0x0,
    0xe5, 0x2d, 0x0, 0xd, 0x70, 0x0, 0x4e, 0x0,
    0x4e, 0x2, 0xf0, 0xb, 0x70, 0x7e, 0x10, 0x0,
    0x4f, 0x99, 0xbe, 0x7, 0xa0, 0x3, 0xe3, 0xf4,
    0x0, 0x0, 0x4e, 0x0, 0x4e, 0xe, 0x30, 0x0,
    0xbf, 0x90, 0x0, 0x0, 0x4e, 0x0, 0x13, 0x69,
    0x0, 0x0, 0xbf, 0xa0, 0x0, 0x0, 0x13, 0x0,
    0x2, 0xd0, 0x0, 0x1c, 0x86, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0xb, 0x30, 0x5, 0xc4, 0x0, 0x5f,
    0xf8, 0x10, 0x0, 0x1, 0xa3, 0x5, 0x96, 0x0,
    0x0, 0x2, 0xbf, 0xa0, 0x0, 0x3, 0x10, 0x42,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x0,

    /* U+548C "和" */
    0x0, 0x0, 0x0, 0x4, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x7d, 0xfc, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x8c, 0xdf, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x1, 0x41, 0x1, 0xf2,
    0x0, 0x4, 0xe9, 0x99, 0x9c, 0xf5, 0x0, 0x0,
    0x1f, 0x20, 0x0, 0x4f, 0x0, 0x0, 0x6e, 0x10,
    0x0, 0x1, 0xf2, 0x7, 0x24, 0xf0, 0x0, 0x6,
    0xd0, 0x9, 0x99, 0xaf, 0xaa, 0xfe, 0x5f, 0x0,
    0x0, 0x6d, 0x0, 0x0, 0xd, 0xf2, 0x0, 0x4,
    0xf0, 0x0, 0x6, 0xd0, 0x0, 0x3, 0xff, 0x91,
    0x0, 0x4f, 0x0, 0x0, 0x6d, 0x0, 0x0, 0x8f,
    0xf5, 0xe5, 0x4, 0xf0, 0x0, 0x6, 0xd0, 0x0,
    0xd, 0x7f, 0x24, 0xf4, 0x4f, 0x0, 0x0, 0x6d,
    0x0, 0x4, 0xc2, 0xf2, 0x9, 0x64, 0xf0, 0x0,
    0x6, 0xd0, 0x0, 0xd3, 0x1f, 0x20, 0x0, 0x4f,
    0x0, 0x0, 0x6d, 0x0, 0x76, 0x1, 0xf2, 0x0,
    0x4, 0xf0, 0x0, 0x6, 0xd0, 0x17, 0x0, 0x1f,
    0x20, 0x0, 0x4f, 0x99, 0x99, 0xcd, 0x0, 0x0,
    0x1, 0xf2, 0x0, 0x4, 0xf0, 0x0, 0x6, 0xd0,
    0x0, 0x0, 0x1f, 0x20, 0x0, 0x4f, 0x0, 0x0,
    0x6d, 0x0, 0x0, 0x1, 0xf2, 0x0, 0x3, 0x90,
    0x0, 0x2, 0x20, 0x0, 0x0, 0x1e, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5587 "喇" */
    0x0, 0x0, 0x0, 0x0, 0x27, 0x20, 0x0, 0x0,
    0x43, 0x0, 0x0, 0x0, 0x0, 0x4, 0xe0, 0x0,
    0x0, 0xd, 0x70, 0x4a, 0x67, 0xe5, 0x0, 0x4e,
    0x7, 0x5, 0x40, 0xd4, 0x4, 0xd2, 0x3f, 0x49,
    0x9b, 0xfa, 0xe9, 0xd6, 0xd, 0x40, 0x4d, 0x1,
    0xf0, 0x0, 0x4e, 0x0, 0xd, 0x40, 0xd4, 0x4,
    0xd0, 0x1f, 0x5, 0x4, 0xe0, 0x81, 0xd4, 0xd,
    0x40, 0x4d, 0x1, 0xf0, 0xcb, 0xaf, 0x9f, 0x8d,
    0x40, 0xd4, 0x4, 0xd0, 0x1f, 0xc, 0x53, 0xd0,
    0xe2, 0xd4, 0xd, 0x40, 0x4d, 0x1, 0xf0, 0xc5,
    0x3d, 0xe, 0x2d, 0x40, 0xd4, 0x4, 0xd0, 0x1f,
    0xc, 0x53, 0xd0, 0xe2, 0xd4, 0xd, 0x40, 0x4d,
    0x1, 0xf0, 0xcb, 0xdf, 0x9f, 0x2d, 0x40, 0xd4,
    0x4, 0xd0, 0x1f, 0x9, 0x6f, 0xe0, 0x70, 0xd4,
    0xd, 0x40, 0x4e, 0x89, 0xf0, 0xa, 0xef, 0xa3,
    0xd, 0x40, 0xd4, 0x4, 0xd0, 0x1e, 0x2, 0xf6,
    0xe2, 0xf5, 0xc3, 0xd, 0x40, 0x4d, 0x0, 0x0,
    0xa6, 0x4e, 0x9, 0xe0, 0x0, 0xd4, 0x1, 0x10,
    0x0, 0x6a, 0x4, 0xe0, 0x28, 0x0, 0xd, 0x40,
    0x0, 0x0, 0x29, 0x0, 0x4e, 0x0, 0x0, 0x0,
    0xd4, 0x0, 0x0, 0x4, 0x0, 0x4, 0xe0, 0x0,
    0x27, 0xbf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x4c,
    0x0, 0x0, 0xa, 0xa0, 0x0,

    /* U+56DE "回" */
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0xe, 0xa9, 0x99, 0x99, 0x99, 0x99, 0x99, 0x9f,
    0xb0, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe7, 0xe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x60, 0xe5, 0x0, 0x10, 0x0, 0x0, 0x20,
    0x0, 0xe6, 0xe, 0x50, 0xe, 0xa9, 0x99, 0x9f,
    0xb0, 0xe, 0x60, 0xe5, 0x0, 0xe4, 0x0, 0x0,
    0xe6, 0x0, 0xe6, 0xe, 0x50, 0xe, 0x40, 0x0,
    0xe, 0x50, 0xe, 0x60, 0xe5, 0x0, 0xe4, 0x0,
    0x0, 0xe5, 0x0, 0xe6, 0xe, 0x50, 0xe, 0x40,
    0x0, 0xe, 0x50, 0xe, 0x60, 0xe5, 0x0, 0xe4,
    0x0, 0x0, 0xe5, 0x0, 0xe6, 0xe, 0x50, 0xe,
    0xb9, 0x99, 0x9f, 0x50, 0xe, 0x60, 0xe5, 0x0,
    0xe4, 0x0, 0x0, 0xe5, 0x0, 0xe6, 0xe, 0x50,
    0x4, 0x0, 0x0, 0x1, 0x0, 0xe, 0x60, 0xe5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe6, 0xe,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x60,
    0xeb, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0xf6,
    0xe, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0x40,

    /* U+590D "复" */
    0x0, 0x0, 0xa7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x80, 0x0, 0x0, 0x0,
    0x2, 0x70, 0x0, 0x0, 0xc, 0xf9, 0x99, 0x99,
    0x99, 0x99, 0xdf, 0x80, 0x0, 0x4, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd7,
    0x50, 0x0, 0x0, 0x0, 0x9, 0x20, 0x0, 0x0,
    0x96, 0x3f, 0x99, 0x99, 0x99, 0x99, 0xfa, 0x0,
    0x0, 0x74, 0x3, 0xf0, 0x0, 0x0, 0x0, 0xf,
    0x50, 0x0, 0x0, 0x0, 0x3f, 0x99, 0x99, 0x99,
    0x99, 0xf5, 0x0, 0x0, 0x0, 0x3, 0xf0, 0x0,
    0x0, 0x0, 0xf, 0x50, 0x0, 0x0, 0x0, 0x3f,
    0x99, 0x99, 0x99, 0x99, 0xf5, 0x0, 0x0, 0x0,
    0x2, 0x75, 0xd4, 0x0, 0x0, 0x5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdb, 0x0, 0x0, 0x1, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0xae, 0x99, 0x99, 0x99,
    0xee, 0x50, 0x0, 0x0, 0x0, 0x99, 0x17, 0x0,
    0x0, 0x5e, 0x20, 0x0, 0x0, 0x0, 0x86, 0x0,
    0x28, 0x10, 0x7d, 0x30, 0x0, 0x0, 0x0, 0x41,
    0x0, 0x0, 0x2e, 0xce, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xad, 0x86, 0xcf, 0xea, 0x75,
    0x32, 0x0, 0x37, 0x9a, 0x83, 0x0, 0x0, 0x28,
    0xcf, 0xff, 0x80, 0x43, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x40, 0x0,

    /* U+5916 "外" */
    0x0, 0x0, 0x5, 0x20, 0x0, 0x0, 0x3, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf9, 0x0, 0x0,
    0x2, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0x20, 0x0, 0x0, 0x2f, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xd0, 0x0, 0x65, 0x2, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdb, 0x88, 0x8e, 0xe2,
    0x2f, 0x20, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x10,
    0x0, 0xf7, 0x2, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x9, 0x90, 0x0, 0x3f, 0x30, 0x2f, 0x30, 0x0,
    0x0, 0x0, 0x1, 0xf6, 0x0, 0x6, 0xf0, 0x2,
    0xfb, 0x81, 0x0, 0x0, 0x0, 0x87, 0x8d, 0x30,
    0xab, 0x0, 0x2f, 0x39, 0xe6, 0x0, 0x0, 0x2b,
    0x0, 0xaf, 0x2f, 0x60, 0x2, 0xf2, 0x7, 0xf7,
    0x0, 0x9, 0x10, 0x1, 0xeb, 0xe0, 0x0, 0x2f,
    0x20, 0xa, 0xf1, 0x0, 0x10, 0x0, 0x1, 0xd8,
    0x0, 0x2, 0xf2, 0x0, 0x1b, 0x10, 0x0, 0x0,
    0x0, 0x4f, 0x10, 0x0, 0x2f, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0x70, 0x0, 0x2, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xc0, 0x0,
    0x0, 0x2f, 0x20, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xd1, 0x0, 0x0, 0x2, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xb1, 0x0, 0x0, 0x0, 0x2f, 0x20,
    0x0, 0x0, 0x0, 0x7, 0x80, 0x0, 0x0, 0x0,
    0x2, 0xf2, 0x0, 0x0, 0x0, 0x7, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0x10, 0x0, 0x0, 0x0,

    /* U+5B9A "定" */
    0x0, 0x0, 0x0, 0x0, 0x28, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xe1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x1, 0xf4, 0x0, 0x0, 0x3, 0x0, 0x0, 0x5b,
    0x99, 0x99, 0x99, 0xb9, 0x99, 0x99, 0xbf, 0x60,
    0x1, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xad, 0x20, 0xc, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc1, 0x0, 0x1d, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9b, 0x10, 0x0, 0x0, 0x9,
    0x99, 0x99, 0x9f, 0xc9, 0x99, 0x99, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0x0, 0xe, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x30,
    0xe, 0x60, 0x0, 0x98, 0x0, 0x0, 0x0, 0x0,
    0xab, 0x0, 0xe, 0xc9, 0x99, 0xbb, 0x30, 0x0,
    0x0, 0x0, 0xe6, 0x0, 0xe, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xe8, 0x0, 0xe, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0x64, 0x60,
    0xe, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4c,
    0x0, 0x9a, 0x2e, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xb2, 0x0, 0x7, 0xff, 0xb6, 0x55, 0x44,
    0x55, 0x50, 0x9, 0x20, 0x0, 0x0, 0x27, 0xbd,
    0xef, 0xff, 0xff, 0x70, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5BC6 "密" */
    0x0, 0x0, 0x0, 0x0, 0x3b, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x0, 0x0,
    0x1, 0x80, 0x0, 0x0, 0x29, 0x0, 0x0, 0x4d,
    0x99, 0x99, 0xb9, 0x99, 0x99, 0x99, 0xcf, 0x70,
    0x1, 0xe6, 0x0, 0x0, 0x3c, 0x20, 0x5, 0x50,
    0xa7, 0x0, 0xd, 0xe1, 0x0, 0xf6, 0xa, 0xb0,
    0x4f, 0x70, 0x50, 0x0, 0x2, 0x14, 0x50, 0xf3,
    0x3, 0x55, 0xf5, 0x9, 0x50, 0x0, 0x0, 0xa,
    0x50, 0xf3, 0x1, 0xae, 0x30, 0x1, 0xd9, 0x0,
    0x0, 0x9f, 0x20, 0xf3, 0x6e, 0x70, 0x0, 0x50,
    0x3f, 0x50, 0x0, 0xb5, 0x1, 0xfe, 0xc3, 0x11,
    0x22, 0xb2, 0xb, 0x40, 0x0, 0x2, 0x8b, 0xcf,
    0xff, 0xff, 0xfe, 0xd4, 0x0, 0x0, 0x3, 0x75,
    0x10, 0x0, 0x2, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x52, 0x0, 0x6, 0xf1, 0x0, 0x5,
    0x40, 0x0, 0x0, 0x5, 0xf1, 0x0, 0x6, 0xe0,
    0x0, 0xe, 0x80, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x6, 0xe0, 0x0, 0xe, 0x60, 0x0, 0x0, 0x5,
    0xe0, 0x0, 0x6, 0xe0, 0x0, 0xe, 0x60, 0x0,
    0x0, 0x6, 0xe0, 0x0, 0x6, 0xe0, 0x0, 0xe,
    0x60, 0x0, 0x0, 0xa, 0xf9, 0x99, 0x99, 0x99,
    0x99, 0x9f, 0x60, 0x0, 0x0, 0x0, 0x30, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x50, 0x0,

    /* U+5E74 "年" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xe8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x8a, 0x0,
    0x0, 0x0, 0xae, 0xa9, 0x99, 0xbd, 0x99, 0x99,
    0xcc, 0x60, 0x0, 0x3, 0xf3, 0x0, 0x0, 0x6e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0x40, 0x0,
    0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x96,
    0x10, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x80, 0x0,
    0x7, 0x40, 0x6d, 0x99, 0x99, 0xcf, 0x99, 0x9b,
    0xfc, 0x0, 0x1, 0x0, 0x6e, 0x0, 0x0, 0x6e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0x0,
    0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6e, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6e, 0x0, 0x0, 0x6e, 0x0, 0x0,
    0x7, 0x30, 0x7, 0x88, 0xbf, 0x88, 0x88, 0xbf,
    0x88, 0x88, 0x9f, 0xf3, 0x0, 0x11, 0x11, 0x11,
    0x11, 0x7e, 0x11, 0x11, 0x11, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6b, 0x0, 0x0, 0x0, 0x0,

    /* U+5EA6 "度" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xea, 0x0, 0x0, 0x1, 0x0, 0x0, 0x25,
    0x0, 0x0, 0x0, 0x56, 0x0, 0x0, 0x6e, 0x30,
    0x0, 0x3f, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x70, 0x0, 0x3f, 0x10, 0x4, 0xc3, 0x0,
    0xc, 0x80, 0x0, 0x0, 0x0, 0x3f, 0x10, 0x5,
    0xe0, 0x0, 0xe, 0x50, 0x77, 0x0, 0x0, 0x3f,
    0x49, 0x9b, 0xf9, 0x99, 0x9f, 0xb9, 0xcc, 0x30,
    0x0, 0x3f, 0x10, 0x5, 0xe0, 0x0, 0xe, 0x50,
    0x0, 0x0, 0x0, 0x3f, 0x10, 0x5, 0xe0, 0x0,
    0xe, 0x50, 0x0, 0x0, 0x0, 0x3f, 0x0, 0x5,
    0xf9, 0x99, 0x9f, 0x50, 0x0, 0x0, 0x0, 0x4f,
    0x0, 0x5, 0xd0, 0x0, 0xb, 0x30, 0x0, 0x0,
    0x0, 0x5e, 0x2, 0x99, 0x99, 0x99, 0x99, 0xdb,
    0x10, 0x0, 0x0, 0x7c, 0x0, 0x5, 0x40, 0x0,
    0x7, 0xf6, 0x0, 0x0, 0x0, 0x99, 0x0, 0x0,
    0xa2, 0x0, 0x4f, 0x70, 0x0, 0x0, 0x0, 0xd4,
    0x0, 0x0, 0x1c, 0x55, 0xf7, 0x0, 0x0, 0x0,
    0x1, 0xe0, 0x0, 0x0, 0x3, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x6, 0x70, 0x0, 0x2, 0xac, 0x67,
    0xef, 0xa6, 0x31, 0x0, 0xa, 0x0, 0x38, 0x99,
    0x30, 0x0, 0x5, 0xbf, 0xff, 0xb1, 0x22, 0x6,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x36, 0x10,

    /* U+5F00 "开" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb8, 0x0, 0x3, 0x99, 0x99, 0xfc,
    0x99, 0x99, 0xcf, 0x99, 0x99, 0x20, 0x0, 0x0,
    0x0, 0xe7, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe7, 0x0, 0x0, 0x6e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe7, 0x0, 0x0,
    0x6e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe7,
    0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe7, 0x0, 0x0, 0x6e, 0x0, 0x5, 0x30,
    0x18, 0x88, 0x88, 0xfb, 0x88, 0x88, 0xbf, 0x88,
    0x8f, 0xf3, 0x0, 0x0, 0x1, 0xf6, 0x0, 0x0,
    0x7e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf4,
    0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xf2, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xe0, 0x0, 0x0, 0x6e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0x80, 0x0, 0x0,
    0x6e, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x20,
    0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0x0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xa0, 0x0, 0x0, 0x0, 0x6e, 0x0,
    0x0, 0x0, 0x0, 0x88, 0x0, 0x0, 0x0, 0x0,
    0x6e, 0x0, 0x0, 0x0, 0x8, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x6b, 0x0, 0x0, 0x0,

    /* U+5F0F "式" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xca, 0x14, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x80,
    0x4e, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd8, 0x0, 0x3f, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x90, 0x0, 0x29, 0x0, 0x89, 0x99,
    0x99, 0x99, 0x99, 0xed, 0x99, 0x9c, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8d, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xe4, 0x3f, 0x10, 0x0, 0x0, 0x0, 0x88, 0x8b,
    0xf8, 0x88, 0x60, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5e, 0x0, 0x0, 0xb, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xe0, 0x0, 0x0, 0x7e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5e, 0x0, 0x0, 0x2,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x0, 0xa, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x5e,
    0x0, 0x36, 0x30, 0x2f, 0x70, 0x6, 0x0, 0x0,
    0x7, 0xfb, 0xb6, 0x0, 0x0, 0x9f, 0x60, 0xa0,
    0x27, 0xbe, 0xe8, 0x20, 0x0, 0x0, 0x0, 0xaf,
    0xaa, 0x3, 0xe9, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xd0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x59, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5F53 "当" */
    0x0, 0x0, 0x0, 0x0, 0xb9, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0xe, 0x60, 0x0, 0x7,
    0x40, 0x0, 0x98, 0x0, 0x0, 0xe6, 0x0, 0x2,
    0xfb, 0x20, 0x0, 0xcb, 0x0, 0xe, 0x60, 0x0,
    0xad, 0x0, 0x0, 0x2, 0xf7, 0x0, 0xe6, 0x0,
    0x2e, 0x20, 0x0, 0x0, 0x9, 0xe0, 0xe, 0x60,
    0xb, 0x30, 0x0, 0x0, 0x0, 0x28, 0x0, 0xe6,
    0x2, 0x30, 0x2, 0x1, 0x99, 0x99, 0x99, 0x9f,
    0xc9, 0x99, 0x9c, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xe0, 0x0, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0xce, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6e, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xe0,
    0x29, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0xce,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6b, 0x0,

    /* U+5F55 "录" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x69, 0x99, 0x99, 0x99, 0x99,
    0xaf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0x0, 0x0, 0x0, 0x1,
    0x99, 0x99, 0x99, 0x99, 0x9b, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xe0, 0x28, 0x2, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0xcf, 0x9d, 0xf9, 0x0, 0x1, 0x0, 0x0,
    0xe, 0xa0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x2c,
    0x60, 0x0, 0xea, 0x10, 0x1, 0xdb, 0x10, 0x0,
    0x0, 0x1e, 0xb0, 0xe, 0x67, 0x0, 0xcc, 0x10,
    0x0, 0x0, 0x0, 0x4f, 0x30, 0xe5, 0x66, 0xb7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x46, 0x9f, 0x50,
    0xb6, 0x0, 0x0, 0x0, 0x0, 0x1, 0x8d, 0x50,
    0xe5, 0x1, 0xe8, 0x0, 0x0, 0x0, 0x39, 0xf9,
    0x10, 0xe, 0x50, 0x3, 0xec, 0x30, 0x0, 0xbf,
    0xc3, 0x0, 0x0, 0xe5, 0x0, 0x1, 0xdf, 0xc4,
    0x4, 0x70, 0x0, 0x21, 0xf, 0x50, 0x0, 0x0,
    0x7f, 0x50, 0x0, 0x0, 0x3, 0x7f, 0xf3, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x73,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6062 "恢" */
    0x0, 0x5, 0xd3, 0x0, 0x1, 0xe8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xe0, 0x0, 0x2, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x4, 0xf1, 0x0, 0x0, 0x6, 0x20, 0x0, 0x5,
    0xe2, 0x39, 0x9b, 0xf9, 0x99, 0x99, 0xaf, 0xe1,
    0x0, 0x85, 0xe8, 0x60, 0x9, 0xb0, 0x2, 0x0,
    0x0, 0x0, 0x1, 0xb5, 0xe2, 0xf2, 0xd, 0x70,
    0xf, 0x90, 0x0, 0x0, 0x5, 0xb5, 0xe0, 0xf4,
    0x1f, 0x30, 0xf, 0x50, 0x0, 0x0, 0xc, 0x95,
    0xe0, 0x20, 0x4f, 0x2, 0xf, 0x40, 0x16, 0x0,
    0x1c, 0x25, 0xe0, 0x0, 0x8a, 0x19, 0xf, 0x60,
    0x8f, 0x50, 0x0, 0x5, 0xe0, 0x0, 0xc5, 0x49,
    0x2f, 0x91, 0xf5, 0x0, 0x0, 0x5, 0xe0, 0x1,
    0xe0, 0x98, 0x4f, 0xa9, 0x60, 0x0, 0x0, 0x5,
    0xe0, 0x6, 0x83, 0xf3, 0x7d, 0xa7, 0x0, 0x0,
    0x0, 0x5, 0xe0, 0xb, 0x12, 0x50, 0x9a, 0x74,
    0x0, 0x0, 0x0, 0x5, 0xe0, 0x37, 0x0, 0x0,
    0xe3, 0x3b, 0x0, 0x0, 0x0, 0x5, 0xe0, 0x80,
    0x0, 0x6, 0xc0, 0xe, 0x40, 0x0, 0x0, 0x5,
    0xe0, 0x10, 0x0, 0x1d, 0x20, 0x7, 0xe1, 0x0,
    0x0, 0x5, 0xe0, 0x0, 0x1, 0xc3, 0x0, 0x0,
    0xed, 0x20, 0x0, 0x5, 0xe0, 0x0, 0x2b, 0x30,
    0x0, 0x0, 0x4f, 0xf4, 0x0, 0x5, 0xc0, 0x4,
    0x71, 0x0, 0x0, 0x0, 0x4, 0x80,

    /* U+606F "息" */
    0x0, 0x0, 0x0, 0x0, 0x2c, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x7, 0xd1,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xda,
    0x99, 0xdb, 0x99, 0x99, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xd, 0x60, 0x0, 0x0, 0x0, 0xe, 0x70,
    0x0, 0x0, 0x0, 0x0, 0xd6, 0x0, 0x0, 0x0,
    0x0, 0xe6, 0x0, 0x0, 0x0, 0x0, 0xd, 0xc9,
    0x99, 0x99, 0x99, 0x9f, 0x60, 0x0, 0x0, 0x0,
    0x0, 0xd6, 0x0, 0x0, 0x0, 0x0, 0xe6, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xb8, 0x88, 0x88, 0x88,
    0x8f, 0x60, 0x0, 0x0, 0x0, 0x0, 0xd7, 0x0,
    0x0, 0x0, 0x0, 0xe6, 0x0, 0x0, 0x0, 0x0,
    0xd, 0x60, 0x0, 0x0, 0x0, 0xe, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xdb, 0x99, 0x99, 0x99, 0x99,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0xd, 0x50, 0x0,
    0x0, 0x0, 0xe, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x26, 0xb2, 0x9, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x50, 0x7d, 0x0, 0x1d, 0x60, 0x0,
    0x77, 0x0, 0x0, 0x0, 0xa, 0x7, 0xd0, 0x0,
    0x6d, 0x0, 0x40, 0xac, 0x0, 0x0, 0x7, 0x90,
    0x7d, 0x0, 0x0, 0x40, 0xa, 0x0, 0xea, 0x0,
    0x7, 0xf3, 0x7, 0xe3, 0x22, 0x22, 0x34, 0xf1,
    0x6, 0xe0, 0x0, 0x85, 0x0, 0x3e, 0xff, 0xff,
    0xff, 0xec, 0x30, 0x3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+624B "手" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x6b,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x69,
    0xdf, 0xfc, 0xa1, 0x0, 0x0, 0x35, 0x79, 0xbc,
    0xcd, 0xf7, 0x52, 0x0, 0x0, 0x0, 0x0, 0x32,
    0x10, 0x0, 0x6, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xe0,
    0x0, 0x0, 0x81, 0x0, 0x0, 0x79, 0x99, 0x99,
    0x9c, 0xf9, 0x99, 0x9b, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xe0,
    0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xe0, 0x0, 0x0, 0x1d, 0x90, 0x19, 0x99,
    0x99, 0x99, 0x9c, 0xf9, 0x99, 0x99, 0x99, 0x93,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x32, 0x18, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x26, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x55, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+62A5 "报" */
    0x0, 0x4, 0xa3, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x20, 0x0, 0x0, 0x5, 0xe0, 0x0, 0xea, 0x99,
    0x99, 0x9a, 0xf4, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0xe5, 0x0, 0x0, 0x4, 0xe0, 0x0, 0x0, 0x5,
    0xe0, 0x10, 0xe5, 0x0, 0x0, 0x6, 0xd0, 0x0,
    0x4, 0x48, 0xf7, 0xf3, 0xe5, 0x0, 0x0, 0x8,
    0xb0, 0x0, 0x4, 0x48, 0xf4, 0x42, 0xe5, 0x0,
    0x66, 0x6e, 0x70, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0xe5, 0x0, 0x4, 0xfb, 0x0, 0x0, 0x0, 0x5,
    0xe0, 0x0, 0xe5, 0x0, 0x0, 0x0, 0x50, 0x0,
    0x0, 0x5, 0xe2, 0x72, 0xeb, 0xa9, 0x99, 0x9a,
    0xfa, 0x0, 0x0, 0x8, 0xfa, 0x10, 0xe5, 0x43,
    0x0, 0x4, 0xf1, 0x0, 0x18, 0xef, 0xe0, 0x0,
    0xe5, 0x9, 0x0, 0x9, 0xc0, 0x0, 0xe, 0x76,
    0xe0, 0x0, 0xe5, 0xa, 0x10, 0xe, 0x70, 0x0,
    0x1, 0x5, 0xe0, 0x0, 0xe5, 0x4, 0xc0, 0x5f,
    0x10, 0x0, 0x0, 0x5, 0xe0, 0x0, 0xe5, 0x0,
    0xb8, 0xd8, 0x0, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0xe5, 0x0, 0x2f, 0xe0, 0x0, 0x0, 0x0, 0x5,
    0xe0, 0x0, 0xe5, 0x0, 0x1e, 0xf5, 0x0, 0x0,
    0x0, 0x5, 0xe0, 0x0, 0xe5, 0x0, 0xc5, 0x8f,
    0x91, 0x0, 0x6, 0xbe, 0xc0, 0x0, 0xe5, 0x1a,
    0x40, 0x7, 0xfe, 0x70, 0x0, 0x4d, 0x50, 0x0,
    0xd7, 0x70, 0x0, 0x0, 0x2c, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6301 "持" */
    0x0, 0x1, 0x74, 0x0, 0x0, 0x0, 0x25, 0x20,
    0x0, 0x0, 0x0, 0x3, 0xf1, 0x0, 0x0, 0x0,
    0x5e, 0x10, 0x0, 0x0, 0x0, 0x3, 0xf0, 0x0,
    0x0, 0x0, 0x5e, 0x0, 0x31, 0x0, 0x0, 0x3,
    0xf0, 0x0, 0x35, 0x55, 0x9f, 0x55, 0xed, 0x20,
    0x0, 0x3, 0xf0, 0x99, 0x23, 0x33, 0x8e, 0x33,
    0x33, 0x0, 0x9, 0x9a, 0xf9, 0x99, 0x20, 0x0,
    0x5e, 0x0, 0x0, 0x0, 0x0, 0x3, 0xf0, 0x0,
    0x0, 0x0, 0x5e, 0x0, 0xa, 0x90, 0x0, 0x3,
    0xf0, 0x8, 0x99, 0x99, 0x9a, 0x99, 0x9a, 0xa3,
    0x0, 0x3, 0xf1, 0x26, 0x10, 0x0, 0x0, 0x1a,
    0x50, 0x0, 0x0, 0x6, 0xfc, 0x70, 0x0, 0x0,
    0x0, 0x2f, 0x24, 0x30, 0x2a, 0xff, 0xf1, 0x6,
    0x99, 0x99, 0x99, 0xaf, 0xae, 0xf2, 0x1d, 0x53,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x20, 0x0,
    0x0, 0x3, 0xf0, 0x0, 0xa, 0x60, 0x0, 0x2f,
    0x20, 0x0, 0x0, 0x3, 0xf0, 0x0, 0x1, 0xea,
    0x0, 0x2f, 0x20, 0x0, 0x0, 0x3, 0xf0, 0x0,
    0x0, 0x7f, 0x10, 0x2f, 0x20, 0x0, 0x0, 0x3,
    0xf0, 0x0, 0x0, 0x5, 0x0, 0x2f, 0x20, 0x0,
    0x3, 0x36, 0xf0, 0x0, 0x0, 0x2, 0x32, 0x4f,
    0x20, 0x0, 0x1, 0x7f, 0xd0, 0x0, 0x0, 0x0,
    0x3a, 0xfe, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x61, 0x0, 0x0,

    /* U+6309 "按" */
    0x0, 0x1, 0x74, 0x0, 0x0, 0x2, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0x10, 0x0, 0x0,
    0xb, 0x80, 0x0, 0x0, 0x0, 0x0, 0x2, 0xf0,
    0x0, 0x0, 0x0, 0x4f, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0x14, 0x8, 0x43, 0x34, 0xc4, 0x33,
    0x97, 0x0, 0x18, 0x89, 0xfc, 0xf5, 0xe4, 0x33,
    0x33, 0x33, 0x3f, 0xa1, 0x0, 0x11, 0x4f, 0x21,
    0xae, 0x0, 0x3e, 0x60, 0x3, 0xc0, 0x0, 0x0,
    0x2, 0xf0, 0x4, 0x20, 0x8, 0xd0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x2f, 0x0, 0x0, 0x0, 0xd8,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x2, 0xf3, 0x84,
    0x0, 0x3f, 0x30, 0x0, 0x4f, 0x50, 0x0, 0x0,
    0x6f, 0x91, 0x58, 0x8d, 0xd8, 0x88, 0xde, 0x87,
    0x0, 0x18, 0xee, 0xf0, 0x0, 0x0, 0xf5, 0x0,
    0xc, 0xa0, 0x0, 0x1, 0xe6, 0x3f, 0x0, 0x0,
    0x6d, 0x0, 0x1, 0xf5, 0x0, 0x0, 0x1, 0x2,
    0xf0, 0x0, 0xe, 0x60, 0x0, 0x6f, 0x10, 0x0,
    0x0, 0x0, 0x2f, 0x0, 0x1, 0xb8, 0x30, 0xe,
    0x70, 0x0, 0x0, 0x0, 0x2, 0xf0, 0x0, 0x0,
    0x3a, 0xdb, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x91, 0x0, 0x0,
    0x0, 0x3, 0xf0, 0x0, 0x0, 0x1b, 0xc1, 0x4d,
    0xf6, 0x0, 0x0, 0x39, 0xfe, 0x0, 0x2, 0x9b,
    0x50, 0x0, 0x9, 0xf6, 0x0, 0x0, 0x9, 0x40,
    0x27, 0x61, 0x0, 0x0, 0x0, 0x5, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+636E "据" */
    0x0, 0x4, 0xd4, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x5, 0xe0, 0x0, 0xfa, 0x99,
    0x99, 0x99, 0x9f, 0x80, 0x0, 0x5, 0xe0, 0x0,
    0xf4, 0x0, 0x0, 0x0, 0xf, 0x40, 0x0, 0x5,
    0xe0, 0x10, 0xf4, 0x0, 0x0, 0x0, 0xf, 0x40,
    0x3, 0x37, 0xf7, 0xe2, 0xf4, 0x0, 0x0, 0x0,
    0xf, 0x40, 0x6, 0x69, 0xf6, 0x62, 0xfb, 0x99,
    0x9a, 0x99, 0x9f, 0x40, 0x0, 0x5, 0xe0, 0x0,
    0xf4, 0x0, 0xe, 0x90, 0x3, 0x0, 0x0, 0x5,
    0xe0, 0x0, 0xf3, 0x0, 0xe, 0x50, 0x7, 0x10,
    0x0, 0x5, 0xe0, 0x54, 0xfb, 0x99, 0x9f, 0xb9,
    0xaf, 0xd0, 0x0, 0x8, 0xfb, 0x31, 0xf1, 0x0,
    0xe, 0x50, 0x0, 0x0, 0x2a, 0xff, 0xe0, 0x3,
    0xf0, 0x0, 0xe, 0x50, 0x0, 0x0, 0x1e, 0x65,
    0xe0, 0x5, 0xd1, 0x0, 0xe, 0x50, 0x5, 0x0,
    0x1, 0x5, 0xe0, 0x7, 0x96, 0xe8, 0x8c, 0xa8,
    0xaf, 0x90, 0x0, 0x5, 0xe0, 0xb, 0x56, 0xd0,
    0x0, 0x0, 0x2f, 0x20, 0x0, 0x5, 0xe0, 0xe,
    0x6, 0xd0, 0x0, 0x0, 0x2f, 0x10, 0x0, 0x5,
    0xe0, 0x58, 0x6, 0xd0, 0x0, 0x0, 0x2f, 0x10,
    0x5, 0x59, 0xe0, 0xa0, 0x6, 0xe9, 0x99, 0x99,
    0xaf, 0x10, 0x0, 0x9f, 0xc1, 0x30, 0x6, 0xd0,
    0x0, 0x0, 0x2e, 0x10, 0x0, 0x1e, 0x50, 0x0,
    0x6, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+63D0 "提" */
    0x0, 0x2, 0xa5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0, 0x0, 0x2, 0xf1, 0x0, 0xe, 0x99,
    0x99, 0x99, 0xfa, 0x0, 0x0, 0x2, 0xf1, 0x0,
    0xf, 0x30, 0x0, 0x0, 0xe6, 0x0, 0x0, 0x2,
    0xf1, 0x0, 0xf, 0x30, 0x0, 0x0, 0xe5, 0x0,
    0x0, 0x2, 0xf2, 0xc4, 0xf, 0xb9, 0x99, 0x99,
    0xf5, 0x0, 0x9, 0x9a, 0xfa, 0x98, 0x1f, 0x30,
    0x0, 0x0, 0xe5, 0x0, 0x0, 0x2, 0xf1, 0x0,
    0xf, 0xa9, 0x99, 0x99, 0xf5, 0x0, 0x0, 0x2,
    0xf1, 0x0, 0xf, 0x30, 0x0, 0x0, 0xe4, 0x0,
    0x0, 0x2, 0xf3, 0x76, 0x2, 0x0, 0x0, 0x0,
    0x2a, 0x10, 0x0, 0x28, 0xfb, 0x23, 0x99, 0x99,
    0xac, 0x99, 0xac, 0x80, 0x3d, 0xfc, 0xf1, 0x0,
    0x0, 0x0, 0x5e, 0x0, 0x0, 0x0, 0xa, 0x22,
    0xf1, 0x0, 0xe, 0xb0, 0x5e, 0x0, 0x51, 0x0,
    0x0, 0x2, 0xf1, 0x0, 0xf, 0x30, 0x5f, 0x88,
    0xfe, 0x10, 0x0, 0x2, 0xf1, 0x0, 0x4f, 0x0,
    0x5e, 0x0, 0x0, 0x0, 0x0, 0x2, 0xf1, 0x0,
    0xaa, 0x70, 0x5e, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xf1, 0x2, 0xd0, 0xb5, 0x5e, 0x0, 0x0, 0x0,
    0x4, 0x47, 0xf0, 0xa, 0x40, 0x2e, 0xef, 0x44,
    0x44, 0x41, 0x0, 0x5f, 0xc0, 0x74, 0x0, 0x1,
    0x8d, 0xff, 0xff, 0xd0, 0x0, 0x4, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6570 "数" */
    0x0, 0x0, 0x7, 0x60, 0x0, 0x0, 0x44, 0x0,
    0x0, 0x0, 0x1, 0x91, 0xe, 0x60, 0xb7, 0x0,
    0xbe, 0x20, 0x0, 0x0, 0x0, 0x6d, 0xe, 0x53,
    0xe1, 0x0, 0xe9, 0x0, 0x0, 0x0, 0x0, 0xe,
    0x1e, 0x59, 0x33, 0x2, 0xf4, 0x0, 0x0, 0x0,
    0x6, 0x66, 0x6f, 0x98, 0x7f, 0x95, 0xf1, 0x0,
    0xc, 0x90, 0x3, 0x33, 0xdf, 0xb4, 0x33, 0x29,
    0xd9, 0x99, 0xfc, 0x92, 0x0, 0x7, 0xef, 0x7d,
    0x70, 0xd, 0x40, 0x0, 0xf6, 0x0, 0x0, 0x4d,
    0x2e, 0x52, 0xf7, 0x3d, 0x60, 0x3, 0xf3, 0x0,
    0x6, 0xa1, 0xe, 0x40, 0x67, 0x92, 0x90, 0x6,
    0xf0, 0x0, 0x13, 0x0, 0x5c, 0x30, 0x1, 0x60,
    0x90, 0x9, 0xc0, 0x0, 0x0, 0x0, 0xd8, 0x0,
    0x42, 0x0, 0x66, 0xf, 0x70, 0x0, 0x9, 0x9c,
    0xfa, 0x99, 0xfc, 0x0, 0x1d, 0x5f, 0x10, 0x0,
    0x0, 0xe, 0x60, 0x2, 0xf2, 0x0, 0xa, 0xe9,
    0x0, 0x0, 0x0, 0x8c, 0x0, 0x9, 0xb0, 0x0,
    0x7, 0xf2, 0x0, 0x0, 0x0, 0x59, 0xca, 0x7e,
    0x10, 0x0, 0x3f, 0xcc, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xfe, 0x40, 0x2, 0xd3, 0x1e, 0xb0, 0x0,
    0x0, 0x0, 0x6c, 0x39, 0xf0, 0x3c, 0x20, 0x3,
    0xfd, 0x30, 0x0, 0x69, 0x60, 0x0, 0x37, 0x80,
    0x0, 0x0, 0x3e, 0xe2, 0x5, 0x10, 0x0, 0x0,
    0x41, 0x0, 0x0, 0x0, 0x1, 0x20,

    /* U+65E5 "日" */
    0x30, 0x0, 0x0, 0x0, 0x0, 0x6, 0xf, 0xb9,
    0x99, 0x99, 0x99, 0x9b, 0xf8, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0x1f, 0x50, 0x0, 0x0, 0x0,
    0x4, 0xf0, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xf, 0x50, 0x0, 0x0, 0x0, 0x4, 0xf0, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xf, 0x50, 0x0,
    0x0, 0x0, 0x4, 0xf0, 0xfb, 0x99, 0x99, 0x99,
    0x99, 0xbf, 0xf, 0x50, 0x0, 0x0, 0x0, 0x4,
    0xf0, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf,
    0x50, 0x0, 0x0, 0x0, 0x4, 0xf0, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf, 0x50, 0x0, 0x0,
    0x0, 0x4, 0xf0, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xf, 0xb9, 0x99, 0x99, 0x99, 0x9b, 0xf0,
    0xe4, 0x0, 0x0, 0x0, 0x0, 0x4d, 0x0,

    /* U+65F6 "时" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x63,
    0x0, 0x2, 0x30, 0x0, 0x81, 0x0, 0x0, 0x0,
    0x6f, 0x10, 0x0, 0x4f, 0x99, 0x9f, 0xb0, 0x0,
    0x0, 0x6, 0xe0, 0x0, 0x4, 0xe0, 0x0, 0xf4,
    0x0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x4e, 0x0,
    0xf, 0x40, 0x0, 0x0, 0x6, 0xe0, 0xb8, 0x4,
    0xe0, 0x0, 0xf5, 0x99, 0x99, 0x99, 0xcf, 0x9b,
    0xb3, 0x4e, 0x0, 0xf, 0x40, 0x0, 0x0, 0x6,
    0xe0, 0x0, 0x4, 0xe0, 0x0, 0xf4, 0x0, 0x0,
    0x0, 0x6e, 0x0, 0x0, 0x4f, 0x99, 0x9f, 0x40,
    0x70, 0x0, 0x6, 0xe0, 0x0, 0x4, 0xe0, 0x0,
    0xf4, 0x5, 0xd2, 0x0, 0x6e, 0x0, 0x0, 0x4e,
    0x0, 0xf, 0x40, 0x9, 0xd0, 0x6, 0xe0, 0x0,
    0x4, 0xe0, 0x0, 0xf4, 0x0, 0x2f, 0x50, 0x6e,
    0x0, 0x0, 0x4e, 0x0, 0xf, 0x40, 0x0, 0xd5,
    0x6, 0xe0, 0x0, 0x4, 0xf8, 0x88, 0xf4, 0x0,
    0x0, 0x0, 0x6e, 0x0, 0x0, 0x4e, 0x0, 0xf,
    0x40, 0x0, 0x0, 0x6, 0xe0, 0x0, 0x4, 0xe0,
    0x0, 0xa1, 0x0, 0x0, 0x0, 0x6e, 0x0, 0x0,
    0x38, 0x0, 0x0, 0x0, 0x0, 0x66, 0x5a, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x22, 0x0, 0x0, 0x0,

    /* U+663E "显" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x0, 0x0, 0x0, 0x2, 0xe9, 0x99, 0x99, 0x99,
    0x99, 0xbf, 0x70, 0x0, 0x0, 0x2, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0x20, 0x0, 0x0, 0x2,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x10, 0x0,
    0x0, 0x2, 0xfa, 0x99, 0x99, 0x99, 0x99, 0xaf,
    0x10, 0x0, 0x0, 0x2, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x10, 0x0, 0x0, 0x2, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0x10, 0x0, 0x0, 0x2,
    0xfa, 0x99, 0x99, 0x99, 0x99, 0xbf, 0x10, 0x0,
    0x0, 0x2, 0xf1, 0x5, 0x40, 0x2, 0x51, 0x3c,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x70, 0x5,
    0xf1, 0x0, 0x10, 0x0, 0x0, 0x55, 0x0, 0xe,
    0x50, 0x5, 0xe0, 0x2, 0xf8, 0x0, 0x0, 0xc,
    0x60, 0xe, 0x50, 0x5, 0xe0, 0x8, 0xe0, 0x0,
    0x0, 0x3, 0xf3, 0xe, 0x50, 0x5, 0xe0, 0xe,
    0x50, 0x0, 0x0, 0x0, 0xca, 0xe, 0x50, 0x5,
    0xe0, 0x6a, 0x0, 0x0, 0x0, 0x0, 0x6e, 0xe,
    0x50, 0x5, 0xe0, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xe, 0x50, 0x5, 0xe2, 0x20, 0x1, 0x0,
    0x0, 0x0, 0x0, 0xe, 0x50, 0x5, 0xe0, 0x0,
    0x4f, 0x50, 0x8, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x90,

    /* U+6708 "月" */
    0x0, 0x0, 0x7, 0x10, 0x0, 0x0, 0x0, 0x92,
    0x0, 0x0, 0x0, 0xfb, 0x99, 0x99, 0x99, 0x9f,
    0xc0, 0x0, 0x0, 0xf, 0x40, 0x0, 0x0, 0x0,
    0xe6, 0x0, 0x0, 0x0, 0xf4, 0x0, 0x0, 0x0,
    0xe, 0x60, 0x0, 0x0, 0xf, 0x40, 0x0, 0x0,
    0x0, 0xe6, 0x0, 0x0, 0x0, 0xfb, 0x99, 0x99,
    0x99, 0x9f, 0x60, 0x0, 0x0, 0xf, 0x40, 0x0,
    0x0, 0x0, 0xe6, 0x0, 0x0, 0x0, 0xf4, 0x0,
    0x0, 0x0, 0xe, 0x60, 0x0, 0x0, 0xf, 0x30,
    0x0, 0x0, 0x0, 0xe6, 0x0, 0x0, 0x1, 0xf2,
    0x0, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0, 0x3f,
    0x99, 0x99, 0x99, 0x99, 0xf6, 0x0, 0x0, 0x6,
    0xd0, 0x0, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0,
    0xa9, 0x0, 0x0, 0x0, 0x0, 0xe6, 0x0, 0x0,
    0x1f, 0x20, 0x0, 0x0, 0x0, 0xe, 0x60, 0x0,
    0x9, 0x90, 0x0, 0x0, 0x0, 0x0, 0xe6, 0x0,
    0x3, 0xc0, 0x0, 0x0, 0x0, 0x10, 0xf, 0x60,
    0x2, 0x91, 0x0, 0x0, 0x0, 0x14, 0xbf, 0xf4,
    0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x95,
    0x0,

    /* U+6807 "标" */
    0x0, 0x3, 0xa4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2b, 0x10, 0x0, 0x0, 0x4, 0xf0,
    0x0, 0x69, 0x99, 0x99, 0x9a, 0xb7, 0x0, 0x0,
    0x0, 0x4f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf1, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x99, 0xdf, 0x99,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0x30, 0x0, 0x1, 0xff, 0x30, 0x79, 0x99, 0x9f,
    0xb9, 0x99, 0x97, 0x0, 0x0, 0x5f, 0xfa, 0x70,
    0x0, 0x0, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xef, 0x2f, 0x51, 0x50, 0xe, 0x50, 0x30, 0x0,
    0x0, 0x0, 0xd7, 0xf0, 0xb7, 0x5f, 0x50, 0xe5,
    0x8, 0x40, 0x0, 0x0, 0x4b, 0x4f, 0x0, 0xa,
    0xa0, 0xe, 0x50, 0xe, 0x30, 0x0, 0xb, 0x34,
    0xf0, 0x0, 0xe4, 0x0, 0xe5, 0x0, 0x6e, 0x10,
    0x2, 0x70, 0x4f, 0x0, 0x3d, 0x0, 0xe, 0x50,
    0x0, 0xe9, 0x0, 0x30, 0x4, 0xf0, 0x9, 0x40,
    0x0, 0xe5, 0x0, 0x7, 0xf0, 0x0, 0x0, 0x4f,
    0x1, 0x80, 0x0, 0xe, 0x50, 0x0, 0x3f, 0x20,
    0x0, 0x4, 0xf0, 0x70, 0x0, 0x0, 0xe5, 0x0,
    0x0, 0x70, 0x0, 0x0, 0x4f, 0x0, 0x0, 0x48,
    0xbf, 0x40, 0x0, 0x0, 0x0, 0x0, 0x4, 0xd0,
    0x0, 0x0, 0xc, 0xa0, 0x0, 0x0, 0x0, 0x0,

    /* U+6821 "校" */
    0x0, 0x0, 0x97, 0x0, 0x0, 0x1, 0x81, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x40, 0x0, 0x0,
    0x7, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf4,
    0x0, 0x0, 0x0, 0x1f, 0x70, 0x0, 0x0, 0x0,
    0x0, 0xf, 0x40, 0x0, 0x0, 0x0, 0x83, 0x1,
    0xc8, 0x0, 0x0, 0x0, 0xf4, 0xa8, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x81, 0x0, 0x99, 0xcf, 0xb9,
    0x91, 0x5, 0x71, 0x0, 0x82, 0x0, 0x0, 0x0,
    0x9, 0xf6, 0x0, 0x0, 0xdb, 0x0, 0x2, 0xe5,
    0x0, 0x0, 0x0, 0xdf, 0xe7, 0x0, 0x4f, 0x10,
    0x0, 0x4, 0xf5, 0x0, 0x0, 0x2f, 0xf7, 0xf4,
    0xb, 0x60, 0x0, 0x5, 0x2a, 0xe0, 0x0, 0x6,
    0xcf, 0x4c, 0x85, 0xa6, 0x0, 0x0, 0xeb, 0x5f,
    0x0, 0x0, 0xc4, 0xf4, 0x34, 0x80, 0x43, 0x0,
    0x2f, 0x20, 0x0, 0x0, 0x4c, 0xf, 0x40, 0x10,
    0x0, 0x90, 0x7, 0xd0, 0x0, 0x0, 0xb, 0x20,
    0xf4, 0x0, 0x0, 0x9, 0x40, 0xe7, 0x0, 0x0,
    0x3, 0x50, 0xf, 0x40, 0x0, 0x0, 0x1d, 0xad,
    0x0, 0x0, 0x0, 0x10, 0x0, 0xf4, 0x0, 0x0,
    0x0, 0xaf, 0x30, 0x0, 0x0, 0x0, 0x0, 0xf,
    0x40, 0x0, 0x0, 0x7d, 0xae, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xf4, 0x0, 0x1, 0xaa, 0x10, 0x8f,
    0x92, 0x0, 0x0, 0x0, 0xf, 0x40, 0x6, 0xb4,
    0x0, 0x0, 0x6e, 0xfb, 0x30, 0x0, 0x0, 0xe3,
    0x38, 0x40, 0x0, 0x0, 0x0, 0x7, 0x90, 0x0,

    /* U+68C0 "检" */
    0x0, 0x0, 0xa6, 0x0, 0x0, 0x0, 0x85, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf3, 0x0, 0x0, 0x6,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf3, 0x0,
    0x0, 0xd, 0x97, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xf3, 0x0, 0x0, 0x5e, 0x10, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0xf3, 0x8a, 0x0, 0xd5, 0x0, 0x5c,
    0x10, 0x0, 0x9, 0x9c, 0xfb, 0x99, 0x39, 0x80,
    0x0, 0x8, 0xe3, 0x0, 0x0, 0x9, 0xf3, 0x0,
    0x59, 0x0, 0x0, 0x0, 0x9f, 0x91, 0x0, 0xd,
    0xf5, 0x4, 0x80, 0x0, 0x0, 0x2a, 0x6, 0xc2,
    0x0, 0x1f, 0xfc, 0x72, 0x6, 0x99, 0x99, 0x99,
    0x40, 0x0, 0x0, 0x5d, 0xf4, 0xf5, 0x0, 0x4,
    0x0, 0x2, 0x61, 0x0, 0x0, 0xb6, 0xf3, 0x98,
    0x80, 0x8, 0x40, 0x7, 0xf4, 0x0, 0x2, 0xd0,
    0xf3, 0x0, 0x5a, 0x3, 0xd0, 0xa, 0xb0, 0x0,
    0xa, 0x30, 0xf3, 0x0, 0xf, 0x20, 0xf3, 0xe,
    0x50, 0x0, 0x27, 0x0, 0xf3, 0x0, 0xd, 0x70,
    0xe6, 0x2e, 0x0, 0x0, 0x0, 0x0, 0xf3, 0x0,
    0xb, 0x90, 0xb5, 0x68, 0x0, 0x0, 0x0, 0x0,
    0xf3, 0x0, 0x5, 0x40, 0x0, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x80,
    0xb, 0x40, 0x0, 0x0, 0xf3, 0x9, 0x99, 0x99,
    0x99, 0x99, 0x9a, 0x90, 0x0, 0x0, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6C14 "气" */
    0x0, 0x0, 0x8, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xca, 0x0, 0x0, 0x0,
    0xec, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x30,
    0x0, 0x4, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x8,
    0x0, 0x0, 0x0, 0xb, 0x78, 0x99, 0x99, 0x99,
    0x99, 0xbf, 0xb0, 0x0, 0x0, 0x4d, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x30, 0x0, 0x0,
    0x7, 0x38, 0x99, 0x99, 0x99, 0x99, 0x9d, 0xf3,
    0x0, 0x0, 0x4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xd0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9e, 0x0, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xb1, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3a, 0xc3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6C22 "氢" */
    0x0, 0x0, 0x1b, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x58, 0x0, 0x0, 0x1, 0xed, 0x99,
    0x99, 0x99, 0x99, 0x99, 0xdd, 0x70, 0x0, 0x8,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x20, 0x0,
    0x0, 0x3d, 0x29, 0x99, 0x99, 0x99, 0x99, 0xad,
    0xc1, 0x0, 0x1, 0xa1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x12, 0x0, 0x0, 0x7, 0x19, 0x99, 0x99,
    0x99, 0x99, 0x99, 0xde, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7d, 0x0, 0x0,
    0x0, 0x29, 0x99, 0x99, 0x99, 0xdd, 0x20, 0x7d,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xf6,
    0x0, 0x6d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xfd, 0x20, 0x0, 0x5f, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xcd, 0x56, 0xda, 0x40, 0x2f, 0x0, 0x0,
    0x0, 0x5, 0xcb, 0x40, 0x0, 0x19, 0xf9, 0xf,
    0x30, 0x0, 0x4, 0x85, 0x10, 0x0, 0x0, 0x99,
    0x4b, 0xe, 0x60, 0x0, 0x0, 0x5, 0x99, 0x9f,
    0xb9, 0x99, 0x30, 0x9, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x50, 0x0, 0x0, 0x4, 0xf1, 0x51,
    0x0, 0x0, 0x0, 0xe, 0x50, 0x0, 0x39, 0x0,
    0xcc, 0xb0, 0x4, 0x99, 0x99, 0x9d, 0xa9, 0x99,
    0xcd, 0x70, 0x1e, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x92,

    /* U+6C27 "氧" */
    0x0, 0x0, 0x57, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0xa2, 0x0, 0x0, 0x6, 0xfb, 0x99, 0x99,
    0x99, 0x99, 0xbe, 0xd1, 0x0, 0x1, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0x36, 0x0, 0x0, 0x0, 0xc5,
    0x79, 0x99, 0x99, 0x99, 0x9e, 0xf7, 0x0, 0x0,
    0x94, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0,
    0x0, 0x64, 0x99, 0x99, 0x99, 0x99, 0x99, 0x9e,
    0xb0, 0x0, 0x0, 0x0, 0x26, 0x0, 0x8, 0x81,
    0x0, 0xd9, 0x0, 0x0, 0x0, 0x0, 0xba, 0x0,
    0xf7, 0x0, 0xc, 0x80, 0x0, 0x0, 0x0, 0x5,
    0xe0, 0x57, 0xa, 0xb1, 0xb8, 0x0, 0x0, 0x6,
    0x99, 0x99, 0xfc, 0x99, 0x99, 0x4a, 0x90, 0x0,
    0x0, 0x0, 0x0, 0xe, 0x60, 0x1a, 0x10, 0x8b,
    0x0, 0x0, 0x0, 0x89, 0x99, 0xfb, 0x9b, 0xec,
    0x6, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x60,
    0x0, 0x10, 0x3f, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xe6, 0x0, 0xd, 0xa0, 0xe6, 0x0, 0x2, 0x99,
    0x99, 0x9f, 0xb9, 0x99, 0x99, 0x39, 0xc0, 0x60,
    0x0, 0x0, 0x0, 0xe6, 0x0, 0x0, 0x0, 0x2f,
    0x9b, 0x0, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0,
    0x0, 0x6f, 0xe0, 0x0, 0x0, 0x0, 0xd4, 0x0,
    0x0, 0x0, 0x0, 0x4a, 0x10,

    /* U+6CF5 "泵" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x80, 0x0, 0x49, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x99, 0xcf, 0xb0, 0x0, 0x0, 0x0, 0xdd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x20, 0x0, 0x0, 0x1, 0x40, 0x0, 0x0, 0x0,
    0x9f, 0xd9, 0x99, 0x99, 0x99, 0xcf, 0x50, 0x0,
    0x1, 0xbb, 0xe6, 0x0, 0x0, 0x0, 0x5, 0xf0,
    0x0, 0x3, 0xa3, 0xd, 0x60, 0x0, 0x0, 0x0,
    0x5e, 0x0, 0x0, 0x30, 0x0, 0xdc, 0x99, 0x99,
    0x99, 0x9b, 0xe0, 0x0, 0x0, 0x0, 0xd, 0x50,
    0x4, 0x30, 0x0, 0x5a, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0x50, 0x0, 0x8, 0x80,
    0x0, 0x0, 0x0, 0x5, 0x70, 0xeb, 0x0, 0x9,
    0xfa, 0x30, 0x6, 0x99, 0x99, 0xfd, 0x2e, 0x77,
    0x2c, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x7e, 0x10,
    0xe5, 0x7b, 0x10, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0x40, 0xe, 0x50, 0xa9, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0x40, 0x0, 0xe5, 0x0, 0x9e, 0x82, 0x0,
    0x0, 0x8b, 0x20, 0x0, 0xe, 0x50, 0x0, 0x4d,
    0xfe, 0x81, 0x84, 0x0, 0x5, 0x9c, 0xf4, 0x0,
    0x0, 0x5, 0xc3, 0x0, 0x0, 0x0, 0x0, 0xa7,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6D4B "测" */
    0x0, 0x53, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x7, 0x60, 0x1, 0xda, 0xd, 0x98, 0x88, 0x9f,
    0x50, 0x0, 0xe4, 0x0, 0x4, 0xf3, 0xe3, 0x0,
    0x4, 0xe0, 0x13, 0xe, 0x40, 0x0, 0x3, 0xe,
    0x20, 0x10, 0x3e, 0x5, 0xd1, 0xe4, 0x0, 0x0,
    0x6, 0xe2, 0x1f, 0x63, 0xe0, 0x5b, 0xe, 0x40,
    0xb7, 0x4, 0x3e, 0x21, 0xf0, 0x3e, 0x5, 0xb0,
    0xe4, 0x1, 0xf8, 0x90, 0xe2, 0x1f, 0x3, 0xe0,
    0x5b, 0xe, 0x40, 0x6, 0x6a, 0xe, 0x22, 0xf0,
    0x3e, 0x5, 0xb0, 0xe4, 0x0, 0x3, 0x80, 0xe2,
    0x2f, 0x3, 0xe0, 0x5b, 0xe, 0x40, 0x0, 0x94,
    0xe, 0x22, 0xf0, 0x3e, 0x5, 0xb0, 0xe4, 0x0,
    0xe, 0x10, 0xe2, 0x4e, 0x3, 0xe0, 0x5b, 0xe,
    0x40, 0x3, 0xd0, 0xe, 0x25, 0xc0, 0x3e, 0x5,
    0xb0, 0xe4, 0x0, 0x9a, 0x0, 0xe2, 0x7a, 0x2,
    0x80, 0x5b, 0xe, 0x40, 0x8f, 0x80, 0x1, 0xa,
    0x85, 0x0, 0x5, 0xb0, 0xe4, 0x0, 0xb9, 0x0,
    0x0, 0xf2, 0x7c, 0x10, 0x22, 0xe, 0x40, 0x9,
    0xc0, 0x0, 0x6b, 0x0, 0xbd, 0x0, 0x0, 0xe4,
    0x0, 0x9d, 0x0, 0x1d, 0x20, 0x2, 0xf2, 0x0,
    0xe, 0x40, 0xa, 0xf0, 0x1c, 0x30, 0x0, 0x4,
    0x1, 0x7a, 0xf2, 0x0, 0x89, 0x29, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xaa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+706F "灯" */
    0x0, 0x0, 0x36, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7d, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0x60, 0x0, 0x0, 0x7c, 0x5,
    0x99, 0x99, 0x99, 0xda, 0x9b, 0xb1, 0x0, 0x0,
    0x7c, 0x0, 0x0, 0x0, 0x1, 0xf2, 0x0, 0x0,
    0x0, 0x10, 0x7c, 0x4, 0x60, 0x0, 0x1, 0xf2,
    0x0, 0x0, 0x0, 0x61, 0x7c, 0xd, 0xc3, 0x0,
    0x1, 0xf2, 0x0, 0x0, 0x0, 0x92, 0x7b, 0x9a,
    0x0, 0x0, 0x1, 0xf2, 0x0, 0x0, 0x1, 0xf1,
    0x8e, 0x60, 0x0, 0x0, 0x1, 0xf2, 0x0, 0x0,
    0xc, 0xd0, 0x8a, 0x0, 0x0, 0x0, 0x1, 0xf2,
    0x0, 0x0, 0xa, 0x20, 0x98, 0x0, 0x0, 0x0,
    0x1, 0xf2, 0x0, 0x0, 0x0, 0x0, 0xb7, 0x0,
    0x0, 0x0, 0x1, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0xc5, 0x0, 0x0, 0x0, 0x1, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0xfa, 0x50, 0x0, 0x0, 0x1, 0xf2,
    0x0, 0x0, 0x0, 0x3, 0xd0, 0xcb, 0x0, 0x0,
    0x1, 0xf2, 0x0, 0x0, 0x0, 0x9, 0x70, 0x1e,
    0xa0, 0x0, 0x1, 0xf2, 0x0, 0x0, 0x0, 0x1d,
    0x0, 0x5, 0xf0, 0x0, 0x1, 0xf2, 0x0, 0x0,
    0x0, 0xa3, 0x0, 0x0, 0x40, 0x15, 0x45, 0xf2,
    0x0, 0x0, 0x8, 0x40, 0x0, 0x0, 0x0, 0x1,
    0x8f, 0xe0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0x10, 0x0, 0x0,

    /* U+71C3 "燃" */
    0x0, 0x6, 0x60, 0x0, 0x48, 0x20, 0x0, 0x66,
    0x0, 0x0, 0x0, 0x0, 0xc9, 0x0, 0xb, 0xc0,
    0x0, 0xc, 0x76, 0x20, 0x0, 0x0, 0xc, 0x80,
    0x0, 0xf6, 0x4, 0x20, 0xc6, 0x2f, 0x30, 0x0,
    0x0, 0xc8, 0x23, 0x4f, 0x99, 0xec, 0xc, 0x60,
    0xa5, 0x0, 0x1, 0x1c, 0x89, 0xca, 0xa0, 0xe,
    0x50, 0xd5, 0x5, 0x0, 0x0, 0x44, 0xc9, 0xc1,
    0xda, 0x82, 0xfa, 0x9f, 0xba, 0xfb, 0x0, 0x5,
    0x6c, 0xc1, 0x68, 0xb, 0x8b, 0x0, 0xf9, 0x0,
    0x0, 0x0, 0xa7, 0xc7, 0xc, 0x81, 0xc, 0x50,
    0x3f, 0x80, 0x0, 0x0, 0x1f, 0x3c, 0x67, 0x26,
    0xd3, 0xd0, 0x7, 0xb6, 0x60, 0x0, 0x0, 0x40,
    0xd6, 0x20, 0x7, 0xb5, 0x0, 0xe3, 0x1d, 0x0,
    0x0, 0x0, 0xe, 0x40, 0x0, 0x79, 0x0, 0x8a,
    0x0, 0xc8, 0x0, 0x0, 0x0, 0xf2, 0x0, 0x4a,
    0x0, 0x5a, 0x0, 0x4, 0xf7, 0x0, 0x0, 0x3f,
    0xa2, 0x67, 0x0, 0x87, 0x0, 0x0, 0x9, 0xf3,
    0x0, 0x6, 0xb5, 0xf4, 0x0, 0x21, 0x0, 0x0,
    0x0, 0x3, 0x0, 0x0, 0x96, 0xd, 0x76, 0x21,
    0x70, 0x43, 0x5, 0x90, 0x0, 0x0, 0xd, 0x10,
    0x43, 0xb2, 0xe, 0x10, 0xd4, 0x8, 0xc1, 0x0,
    0x5, 0x80, 0x0, 0x5f, 0x0, 0xd4, 0x7, 0xd0,
    0xd, 0xa0, 0x1, 0xa0, 0x0, 0x4f, 0x60, 0xd,
    0x30, 0x2f, 0x0, 0x5f, 0x0, 0x10, 0x0, 0x1,
    0x30, 0x0, 0x0, 0x0, 0x10, 0x0, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+7801 "码" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xa, 0x28, 0x88,
    0x88, 0x8a, 0xf5, 0x0, 0x9, 0x99, 0xca, 0xac,
    0xa0, 0x0, 0x0, 0x5, 0xe1, 0x0, 0x0, 0x4,
    0xf3, 0x0, 0x0, 0x57, 0x10, 0x6, 0xd0, 0x0,
    0x0, 0x8, 0xe0, 0x0, 0x0, 0x9a, 0x0, 0x7,
    0xb0, 0x0, 0x0, 0xc, 0x90, 0x0, 0x0, 0xb7,
    0x0, 0x9, 0xa0, 0x0, 0x0, 0xf, 0x30, 0x0,
    0x0, 0xd5, 0x0, 0xb, 0x80, 0x0, 0x0, 0x4d,
    0x0, 0x26, 0x0, 0xf3, 0x0, 0xc, 0x70, 0x0,
    0x0, 0x9f, 0x99, 0xbf, 0x41, 0xf0, 0x0, 0xe,
    0x51, 0x0, 0x0, 0xee, 0x0, 0x5d, 0x9, 0xf9,
    0x99, 0x9f, 0xad, 0xc1, 0x6, 0x7e, 0x0, 0x5d,
    0x1, 0x60, 0x0, 0x0, 0xb, 0x90, 0x7, 0x4e,
    0x0, 0x5d, 0x0, 0x0, 0x0, 0x0, 0xc, 0x70,
    0x20, 0x4e, 0x0, 0x5d, 0x0, 0x0, 0x0, 0x7a,
    0xd, 0x60, 0x0, 0x4e, 0x0, 0x5d, 0x59, 0x99,
    0x99, 0xaa, 0x4e, 0x50, 0x0, 0x4e, 0x0, 0x5d,
    0x0, 0x0, 0x0, 0x0, 0xf, 0x30, 0x0, 0x4f,
    0x99, 0xbd, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x0,
    0x0, 0x4e, 0x0, 0x5d, 0x0, 0x0, 0x0, 0x0,
    0x7d, 0x0, 0x0, 0x39, 0x0, 0x12, 0x0, 0x0,
    0x4, 0x89, 0xe7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1e, 0xa0, 0x0,

    /* U+786B "硫" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x92, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xb2, 0x0, 0x0,
    0x6f, 0x10, 0x0, 0x0, 0x29, 0x9c, 0xba, 0xca,
    0x0, 0x0, 0xc, 0x10, 0x6b, 0x10, 0x0, 0xf,
    0x50, 0x1, 0x99, 0x9a, 0xc9, 0x99, 0xaa, 0x60,
    0x0, 0x3f, 0x20, 0x0, 0x0, 0xb, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x7e,
    0x30, 0x39, 0x10, 0x0, 0x0, 0xa8, 0x0, 0x0,
    0x6, 0xb1, 0x0, 0x5, 0xe4, 0x0, 0x0, 0xe3,
    0x1, 0x70, 0x8f, 0xbb, 0xcc, 0xa8, 0xbf, 0x10,
    0x4, 0xfb, 0x9b, 0xf6, 0x48, 0x64, 0x20, 0x0,
    0x1f, 0x20, 0xa, 0xf4, 0x4, 0xe0, 0xb, 0x60,
    0xb7, 0xb, 0x61, 0x0, 0x1b, 0xe4, 0x4, 0xe0,
    0xe, 0x40, 0xe4, 0xe, 0x40, 0x0, 0x62, 0xd4,
    0x4, 0xe0, 0xf, 0x40, 0xe4, 0xe, 0x40, 0x0,
    0x0, 0xd4, 0x4, 0xe0, 0xf, 0x20, 0xe4, 0xe,
    0x40, 0x0, 0x0, 0xd4, 0x4, 0xe0, 0x1f, 0x0,
    0xe4, 0xe, 0x40, 0x0, 0x0, 0xd4, 0x4, 0xe0,
    0x6b, 0x0, 0xe4, 0xe, 0x40, 0x60, 0x0, 0xdb,
    0x9b, 0xe0, 0xc4, 0x0, 0xe4, 0xe, 0x40, 0x80,
    0x0, 0xd4, 0x3, 0x97, 0x80, 0x0, 0xe4, 0xe,
    0x64, 0x90, 0x0, 0xa2, 0x0, 0x78, 0x0, 0x0,
    0xd3, 0xa, 0xff, 0xb0, 0x0, 0x0, 0x1, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+786E "确" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x40, 0x4f,
    0x30, 0x4, 0x0, 0x0, 0x8, 0x9a, 0xca, 0xad,
    0xd1, 0xbe, 0x99, 0xbf, 0x90, 0x0, 0x0, 0x6,
    0xf1, 0x0, 0x1, 0xf3, 0x0, 0x9c, 0x0, 0x0,
    0x0, 0x9, 0xd0, 0x0, 0x9, 0x70, 0x2, 0xb0,
    0x1, 0x0, 0x0, 0xd, 0x90, 0x0, 0x2e, 0xd9,
    0x9c, 0x99, 0x9e, 0xa0, 0x0, 0xf, 0x30, 0x0,
    0x76, 0xd0, 0xe, 0x40, 0xd, 0x70, 0x0, 0x4e,
    0x0, 0x18, 0x5, 0xd0, 0xe, 0x40, 0xd, 0x60,
    0x0, 0x9f, 0xa9, 0xbf, 0x65, 0xd0, 0xe, 0x40,
    0xd, 0x60, 0x0, 0xee, 0x0, 0x4f, 0x6, 0xf9,
    0x9f, 0xb9, 0x9f, 0x60, 0x5, 0xce, 0x0, 0x4f,
    0x6, 0xd0, 0xe, 0x40, 0xd, 0x60, 0x9, 0x5e,
    0x0, 0x4f, 0x6, 0xc0, 0xe, 0x40, 0xd, 0x60,
    0x2, 0x4e, 0x0, 0x4f, 0x8, 0xe8, 0x8f, 0xa8,
    0x8f, 0x60, 0x0, 0x4e, 0x0, 0x4f, 0xa, 0x91,
    0x1e, 0x51, 0x1e, 0x60, 0x0, 0x4e, 0x0, 0x4f,
    0xd, 0x40, 0xe, 0x40, 0xd, 0x60, 0x0, 0x4f,
    0x99, 0xbf, 0x3d, 0x0, 0xe, 0x40, 0xd, 0x60,
    0x0, 0x4e, 0x0, 0x25, 0xa3, 0x0, 0xe, 0x45,
    0x7f, 0x50, 0x0, 0x39, 0x0, 0x5, 0x60, 0x0,
    0xd, 0x30, 0x9d, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+78B3 "碳" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa4, 0x4f, 0x20,
    0x3e, 0x0, 0x2f, 0x50, 0x29, 0x9c, 0xba, 0xcb,
    0x5e, 0x0, 0x3e, 0x0, 0x2f, 0x0, 0x0, 0xf,
    0x60, 0x0, 0x4e, 0x0, 0x3e, 0x0, 0x2f, 0x0,
    0x0, 0x3f, 0x20, 0x0, 0x8f, 0x99, 0xbf, 0x99,
    0xaf, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x18, 0x51,
    0x0, 0x0, 0x19, 0x0, 0x0, 0xa9, 0x0, 0x0,
    0x2, 0xf4, 0x0, 0x0, 0x8, 0x0, 0x0, 0xd3,
    0x1, 0x75, 0x9b, 0xf9, 0x99, 0x99, 0xcf, 0xa0,
    0x3, 0xfb, 0x9b, 0xf6, 0x5, 0xd0, 0x6, 0x50,
    0x0, 0x0, 0xa, 0xf4, 0x4, 0xe0, 0x7, 0xa0,
    0xd, 0x70, 0x0, 0x0, 0x1b, 0xe4, 0x4, 0xe0,
    0xa, 0x75, 0xd, 0x70, 0x2b, 0x30, 0x62, 0xe4,
    0x4, 0xe0, 0xe, 0x3c, 0xe, 0xa0, 0x9c, 0x10,
    0x10, 0xe4, 0x4, 0xe0, 0x2e, 0x3e, 0x1f, 0xb2,
    0xd1, 0x0, 0x0, 0xe4, 0x4, 0xe0, 0x79, 0xda,
    0x4e, 0x7b, 0x10, 0x0, 0x0, 0xe4, 0x4, 0xe0,
    0xd2, 0x61, 0x98, 0x3b, 0x0, 0x0, 0x0, 0xeb,
    0x9b, 0xe3, 0x90, 0x2, 0xe1, 0xc, 0x80, 0x0,
    0x0, 0xe4, 0x3, 0xaa, 0x10, 0x2c, 0x30, 0x3,
    0xf9, 0x10, 0x0, 0xa2, 0x0, 0x54, 0x5, 0xa2,
    0x0, 0x0, 0x6f, 0xc1, 0x0, 0x0, 0x0, 0x10,
    0x34, 0x0, 0x0, 0x0, 0x3, 0x10,

    /* U+793A "示" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xd4, 0x0, 0x0, 0x0, 0x5, 0x99,
    0x99, 0x99, 0x99, 0x99, 0xab, 0xa1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0x70, 0x0, 0x79, 0x99, 0x99, 0x99, 0xcf, 0x99,
    0x99, 0x99, 0x99, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x0, 0x6e, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xa0, 0x6, 0xe0, 0x2,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x9, 0xe1, 0x0,
    0x6e, 0x0, 0x3, 0xe3, 0x0, 0x0, 0x0, 0x2,
    0xf4, 0x0, 0x6, 0xe0, 0x0, 0x6, 0xf3, 0x0,
    0x0, 0x0, 0xc8, 0x0, 0x0, 0x6e, 0x0, 0x0,
    0xb, 0xe1, 0x0, 0x0, 0x78, 0x0, 0x0, 0x6,
    0xe0, 0x0, 0x0, 0x1f, 0x90, 0x0, 0x46, 0x0,
    0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x9c, 0x0,
    0x3, 0x0, 0x0, 0x11, 0x7, 0xe0, 0x0, 0x0,
    0x1, 0x20, 0x0, 0x0, 0x0, 0x2, 0x6e, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x58, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+79D2 "秒" */
    0x0, 0x0, 0x0, 0x17, 0x10, 0x0, 0x9, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x9f, 0xe9, 0x0,
    0x0, 0xd6, 0x0, 0x0, 0x0, 0x3, 0x8b, 0xde,
    0x30, 0x0, 0x0, 0xd, 0x60, 0x0, 0x0, 0x0,
    0x30, 0x6, 0xd0, 0x0, 0x3, 0x10, 0xd6, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6d, 0x0, 0x0, 0xec,
    0x1d, 0x60, 0xb2, 0x0, 0x0, 0x0, 0x6, 0xd0,
    0x63, 0x1f, 0x40, 0xd6, 0x5, 0xe1, 0x0, 0x9,
    0x99, 0xcf, 0x9e, 0xe5, 0xe0, 0xd, 0x60, 0xd,
    0xa0, 0x0, 0x0, 0x1f, 0xd0, 0x0, 0x79, 0x0,
    0xd6, 0x0, 0x7f, 0x10, 0x0, 0x6, 0xff, 0x50,
    0xb, 0x30, 0xd, 0x60, 0x3, 0xf2, 0x0, 0x0,
    0xbf, 0xdb, 0x80, 0xb0, 0x0, 0xd6, 0x0, 0x75,
    0x0, 0x0, 0xf, 0x9d, 0x2f, 0x82, 0x0, 0xd,
    0x60, 0x5f, 0x70, 0x0, 0x6, 0xa6, 0xd0, 0xd4,
    0x0, 0x0, 0xd5, 0xd, 0xc0, 0x0, 0x0, 0xe2,
    0x6d, 0x0, 0x0, 0x0, 0x2, 0x6, 0xf3, 0x0,
    0x0, 0x85, 0x6, 0xd0, 0x0, 0x0, 0x0, 0x2,
    0xf8, 0x0, 0x0, 0x18, 0x0, 0x6d, 0x0, 0x0,
    0x0, 0x1, 0xda, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xd0, 0x0, 0x0, 0x1, 0xca, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6d, 0x0, 0x0, 0x5, 0xe8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xd0, 0x0, 0x3a,
    0xa2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6b,
    0x3, 0x86, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+7CFB "系" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x8b,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x13, 0x58, 0xbe,
    0xff, 0xda, 0x90, 0x0, 0x4, 0x78, 0x9a, 0xa9,
    0xee, 0x53, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xf8, 0x20, 0x0, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbc, 0x30, 0x0, 0x1c, 0xd2,
    0x0, 0x0, 0x0, 0x0, 0x5c, 0x50, 0x0, 0x5,
    0xed, 0x41, 0x0, 0x0, 0x0, 0xc, 0xea, 0xab,
    0xba, 0xdf, 0x80, 0x0, 0x0, 0x0, 0x0, 0x6,
    0x86, 0x31, 0x7e, 0x91, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5c, 0x81, 0x0, 0x1a, 0x81,
    0x0, 0x0, 0x0, 0x0, 0x6b, 0x60, 0x0, 0x0,
    0x0, 0x8f, 0x70, 0x0, 0x0, 0x7e, 0xb6, 0x78,
    0x9a, 0xba, 0x98, 0x78, 0xf7, 0x0, 0x0, 0x6c,
    0xa8, 0x75, 0x3e, 0x60, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0x0, 0x3, 0x0, 0xe, 0x60, 0x10, 0x0,
    0x4, 0x0, 0x0, 0x0, 0x2f, 0xb0, 0xe, 0x60,
    0x1a, 0x81, 0x0, 0x0, 0x0, 0x0, 0xdb, 0x0,
    0xe, 0x60, 0x0, 0x9e, 0x50, 0x0, 0x0, 0xb,
    0xa0, 0x0, 0xe, 0x60, 0x0, 0x7, 0xf5, 0x0,
    0x1, 0xb6, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0,
    0xaf, 0x0, 0x8, 0x10, 0x0, 0x38, 0xef, 0x40,
    0x0, 0x0, 0x1a, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x75, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7EDF "统" */
    0x0, 0x0, 0x61, 0x0, 0x0, 0x6, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf7, 0x0, 0x0, 0x0,
    0xda, 0x0, 0x0, 0x0, 0x0, 0xb, 0xb0, 0x0,
    0x0, 0x0, 0x4e, 0x0, 0x8, 0x10, 0x0, 0x4e,
    0x10, 0x7, 0x99, 0x99, 0x9a, 0x99, 0xaf, 0xd0,
    0x0, 0xc4, 0x3, 0xd4, 0x0, 0x9, 0xc3, 0x0,
    0x0, 0x0, 0x8, 0x60, 0xc, 0xd1, 0x0, 0x5f,
    0x70, 0x32, 0x0, 0x0, 0x3f, 0xbb, 0xbf, 0x30,
    0x4, 0xe5, 0x0, 0xb, 0x70, 0x0, 0xa, 0x63,
    0xe7, 0x0, 0x6c, 0x20, 0x0, 0x1, 0xe8, 0x0,
    0x0, 0xa, 0x90, 0x8, 0xfb, 0xbc, 0xdc, 0xe9,
    0x9f, 0x20, 0x0, 0x6a, 0x0, 0x3, 0xb8, 0xf8,
    0x11, 0xf3, 0xe, 0x30, 0x4, 0xb0, 0x13, 0x53,
    0x0, 0xe7, 0x1, 0xf3, 0x0, 0x0, 0x2f, 0xef,
    0xc7, 0x20, 0x0, 0xf6, 0x1, 0xf3, 0x0, 0x0,
    0x9, 0x61, 0x0, 0x0, 0x1, 0xf4, 0x1, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x5, 0xf0,
    0x1, 0xf3, 0x0, 0x0, 0x0, 0x1, 0x6a, 0x91,
    0xb, 0xa0, 0x1, 0xf3, 0x0, 0x40, 0x1a, 0xdf,
    0xa2, 0x0, 0x5f, 0x20, 0x1, 0xf3, 0x0, 0x80,
    0xc, 0x91, 0x0, 0x2, 0xe4, 0x0, 0x1, 0xf6,
    0x34, 0xd0, 0x0, 0x0, 0x0, 0x5a, 0x20, 0x0,
    0x0, 0xcf, 0xff, 0xd2, 0x0, 0x0, 0x3, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+7F6E "置" */
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x40, 0x0, 0x0, 0xe, 0xa9, 0x9b, 0xa9, 0x9a,
    0xb9, 0x99, 0xfb, 0x0, 0x0, 0xe, 0x40, 0xd,
    0x50, 0x4, 0xe0, 0x0, 0xf4, 0x0, 0x0, 0xe,
    0x40, 0xd, 0x50, 0x4, 0xe0, 0x0, 0xf4, 0x0,
    0x0, 0xe, 0xb9, 0x9c, 0xa9, 0x9a, 0xc9, 0x99,
    0xf4, 0x0, 0x0, 0xb, 0x20, 0x0, 0x2d, 0x90,
    0x0, 0x0, 0xa9, 0x0, 0x6, 0x99, 0x99, 0x99,
    0xbf, 0xb9, 0x99, 0x99, 0xdf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x7c, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x8c, 0x99, 0xdc, 0x99, 0x99, 0xcf,
    0x60, 0x0, 0x0, 0x0, 0x8a, 0x0, 0x0, 0x0,
    0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x8a, 0x0,
    0x0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0, 0x0,
    0x8d, 0x88, 0x88, 0x88, 0x88, 0xbe, 0x0, 0x0,
    0x0, 0x0, 0x8a, 0x0, 0x0, 0x0, 0x0, 0x6e,
    0x0, 0x0, 0x0, 0x0, 0x8d, 0x99, 0x99, 0x99,
    0x99, 0xce, 0x0, 0x0, 0x0, 0x0, 0x8a, 0x0,
    0x0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0,
    0x8d, 0x99, 0x99, 0x99, 0x99, 0xce, 0x0, 0x0,
    0x0, 0x0, 0x8a, 0x0, 0x0, 0x0, 0x0, 0x6e,
    0x1c, 0x30, 0x9, 0x99, 0xab, 0x99, 0x99, 0x99,
    0x99, 0xac, 0xac, 0xb0,

    /* U+8282 "节" */
    0x0, 0x0, 0x0, 0xa8, 0x0, 0x0, 0xa8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe5, 0x0, 0x0,
    0xe6, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0xe5,
    0x0, 0x0, 0xe6, 0x0, 0x3f, 0x40, 0x29, 0x99,
    0x99, 0xfb, 0x99, 0x99, 0xfb, 0x99, 0x99, 0x80,
    0x0, 0x0, 0x0, 0xe5, 0x0, 0x0, 0xe6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe5, 0x0, 0x0,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x10, 0x7, 0x20, 0x0, 0x0, 0x89,
    0x99, 0x99, 0xfc, 0x99, 0x99, 0x9e, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe6, 0x0, 0x0, 0xd,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe6, 0x0,
    0x0, 0xd, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe6, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe6, 0x0, 0x0, 0xf, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe6, 0x0, 0x0, 0x1f,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe6, 0x1,
    0x10, 0x5f, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe6, 0x2, 0x6f, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe6, 0x0, 0x6, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+83DC "菜" */
    0x0, 0x0, 0x0, 0x4a, 0x40, 0x0, 0x98, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5e, 0x0, 0x0,
    0xe5, 0x0, 0xa, 0x20, 0x8, 0x99, 0x99, 0xbf,
    0x99, 0x99, 0xfb, 0x99, 0xbe, 0xd1, 0x0, 0x0,
    0x0, 0x5e, 0x0, 0x0, 0xe5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x49, 0x0, 0x0, 0x86, 0x9d,
    0x50, 0x0, 0x0, 0x0, 0x13, 0x56, 0x8a, 0xcf,
    0xdb, 0x97, 0x60, 0x0, 0x0, 0x37, 0x66, 0x54,
    0xa4, 0x10, 0x0, 0x1a, 0x20, 0x0, 0x0, 0x4,
    0x90, 0x0, 0x5e, 0x10, 0x0, 0x9e, 0x40, 0x0,
    0x0, 0x0, 0x9d, 0x0, 0xe, 0x70, 0x3, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0x30, 0x7, 0x61,
    0x1c, 0x30, 0x0, 0x0, 0x0, 0x0, 0x5, 0x0,
    0x5, 0xf1, 0x41, 0x0, 0x18, 0x0, 0x6, 0x88,
    0x88, 0x88, 0x8b, 0xf8, 0x88, 0x88, 0xcf, 0xc0,
    0x0, 0x0, 0x0, 0x6, 0xfe, 0xe7, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xc6, 0xe0,
    0xc3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xeb,
    0x5, 0xe0, 0x2e, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0x80, 0x5, 0xe0, 0x2, 0xee, 0x70, 0x0,
    0x0, 0x1a, 0xc3, 0x0, 0x5, 0xe0, 0x0, 0x1a,
    0xff, 0xb2, 0x5, 0xb5, 0x0, 0x0, 0x5, 0xe0,
    0x0, 0x0, 0x4c, 0x80, 0x3, 0x0, 0x0, 0x0,
    0x5, 0xc0, 0x0, 0x0, 0x0, 0x0,

    /* U+8868 "表" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0x60, 0x0, 0x0, 0x80, 0x0, 0x0, 0x69,
    0x99, 0x99, 0x9f, 0xc9, 0x99, 0x9b, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0x60,
    0x0, 0x7, 0x10, 0x0, 0x0, 0x7, 0x99, 0x99,
    0x9f, 0xc9, 0x99, 0xaf, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0x60, 0x0, 0x0,
    0x59, 0x0, 0x7, 0x99, 0x99, 0x99, 0xcf, 0xc9,
    0x99, 0x99, 0xcd, 0x60, 0x0, 0x0, 0x0, 0x7,
    0xf6, 0x70, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0x70, 0x35, 0x0, 0x7, 0xfa, 0x0,
    0x0, 0x0, 0x8, 0xf7, 0x0, 0xb, 0x0, 0x7f,
    0x70, 0x0, 0x0, 0x3, 0xca, 0xf4, 0x0, 0x6,
    0x98, 0xa2, 0x0, 0x0, 0x1, 0x98, 0x10, 0xf4,
    0x0, 0x0, 0xba, 0x0, 0x0, 0x0, 0x16, 0x0,
    0x0, 0xf4, 0x0, 0x0, 0x2f, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf4, 0x0, 0x57, 0x3, 0xee,
    0x71, 0x0, 0x0, 0x0, 0x1, 0xf8, 0xac, 0x40,
    0x0, 0x2d, 0xff, 0xb0, 0x0, 0x0, 0x9, 0xfd,
    0x50, 0x0, 0x0, 0x0, 0x5d, 0x30, 0x0, 0x0,
    0x1, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8B66 "警" */
    0x0, 0xa, 0x70, 0x2e, 0x41, 0x0, 0xa9, 0x0,
    0x0, 0x0, 0x16, 0x6d, 0x76, 0x8f, 0x6e, 0xa1,
    0xf5, 0x0, 0x16, 0x0, 0x3, 0x6d, 0x43, 0x5f,
    0x33, 0x25, 0xf9, 0x99, 0xcf, 0x70, 0x0, 0xad,
    0x44, 0x57, 0x6a, 0xb, 0x70, 0x6, 0xe0, 0x0,
    0x2, 0xe6, 0x67, 0x66, 0xad, 0x77, 0x92, 0xd,
    0x70, 0x0, 0xb, 0xe9, 0x9d, 0xd2, 0x7b, 0x30,
    0x1c, 0x9b, 0x0, 0x0, 0x33, 0xc4, 0x9, 0xa0,
    0x99, 0x0, 0xb, 0xf9, 0x20, 0x0, 0x0, 0xca,
    0x8d, 0xa1, 0xd7, 0x4, 0xa5, 0x2b, 0xfd, 0x90,
    0x0, 0x40, 0x0, 0x2a, 0xf6, 0x53, 0x0, 0x0,
    0x28, 0x30, 0x0, 0x0, 0x0, 0x0, 0x9, 0xe0,
    0x0, 0x0, 0x68, 0x0, 0x8, 0x99, 0x99, 0x99,
    0x9a, 0xc9, 0x99, 0x99, 0xcc, 0x40, 0x0, 0x0,
    0x33, 0x33, 0x33, 0x33, 0x37, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x55, 0x55, 0x55, 0x55, 0x57, 0x92,
    0x0, 0x0, 0x0, 0x0, 0x89, 0x99, 0x99, 0x99,
    0x9c, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x70, 0x0, 0x0, 0x0, 0x0,
    0xeb, 0x99, 0x99, 0x99, 0x9a, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xe5, 0x0, 0x0, 0x0, 0x2, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xeb, 0x99, 0x99, 0x99,
    0x9a, 0xe1, 0x0, 0x0, 0x0, 0x0, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8BA4 "认" */
    0x0, 0x25, 0x0, 0x0, 0x0, 0x4, 0x94, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xb1, 0x0, 0x0, 0x6,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xea, 0x0,
    0x0, 0x6, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6d, 0x0, 0x0, 0x6, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x7, 0xf0, 0x0,
    0x0, 0x0, 0x19, 0x9a, 0xf4, 0x0, 0x0, 0x7,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x0, 0x9, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xe0, 0x0, 0x0, 0xa, 0xa8, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xe0, 0x0, 0x0, 0xd, 0x6b, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xe0, 0x0, 0x0, 0xf,
    0x3b, 0x20, 0x0, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x0, 0x4e, 0x7, 0x70, 0x0, 0x0, 0x0, 0x5,
    0xe0, 0x0, 0x10, 0xa8, 0x2, 0xd0, 0x0, 0x0,
    0x0, 0x5, 0xe0, 0x19, 0x1, 0xf2, 0x0, 0xd7,
    0x0, 0x0, 0x0, 0x5, 0xe3, 0xc1, 0x9, 0x90,
    0x0, 0x6f, 0x20, 0x0, 0x0, 0x6, 0xfe, 0x20,
    0x3d, 0x10, 0x0, 0xd, 0xc0, 0x0, 0x0, 0xd,
    0xf4, 0x1, 0xd2, 0x0, 0x0, 0x4, 0xfb, 0x0,
    0x0, 0x9, 0x50, 0x1b, 0x20, 0x0, 0x0, 0x0,
    0x8f, 0xd2, 0x0, 0x0, 0x3, 0x91, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xb1, 0x0, 0x0, 0x1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8BB0 "记" */
    0x0, 0x25, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x93, 0x0, 0x0, 0x0, 0xcd, 0x4,
    0x99, 0x99, 0x99, 0x99, 0xfd, 0x10, 0x0, 0x0,
    0x3f, 0x20, 0x0, 0x0, 0x0, 0x0, 0xe6, 0x0,
    0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe6, 0x0, 0x8, 0x88, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xe6, 0x0, 0x1, 0x14, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe6, 0x0, 0x0, 0x3,
    0xf0, 0x0, 0x22, 0x0, 0x0, 0x0, 0xe6, 0x0,
    0x0, 0x3, 0xf0, 0x0, 0x5f, 0x99, 0x99, 0x99,
    0xf6, 0x0, 0x0, 0x3, 0xf0, 0x0, 0x5e, 0x0,
    0x0, 0x0, 0xe6, 0x0, 0x0, 0x3, 0xf0, 0x0,
    0x5e, 0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x3,
    0xf0, 0x0, 0x5e, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xf0, 0x0, 0x5e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xf0, 0x17, 0x5e, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x3, 0xf5, 0xb1,
    0x5e, 0x0, 0x0, 0x0, 0x0, 0x70, 0x0, 0x6,
    0xfd, 0x10, 0x5e, 0x0, 0x0, 0x0, 0x1, 0x90,
    0x0, 0xa, 0xd1, 0x0, 0x5f, 0x32, 0x22, 0x22,
    0x28, 0xb0, 0x0, 0x2, 0x20, 0x0, 0x1c, 0xff,
    0xff, 0xff, 0xfe, 0xc1,

    /* U+8BBE "设" */
    0x6, 0x10, 0x0, 0x0, 0x10, 0x0, 0x0, 0x20,
    0x0, 0x0, 0x2e, 0x60, 0x0, 0xe, 0xa9, 0x99,
    0xbf, 0x30, 0x0, 0x0, 0x4f, 0x60, 0x0, 0xe5,
    0x0, 0x5, 0xe0, 0x0, 0x0, 0x0, 0xac, 0x0,
    0xf, 0x40, 0x0, 0x5e, 0x0, 0x0, 0x0, 0x1,
    0x20, 0x0, 0xf3, 0x0, 0x5, 0xe0, 0x0, 0x6,
    0x67, 0xd2, 0x0, 0x4e, 0x0, 0x0, 0x5e, 0x0,
    0x0, 0x23, 0x7e, 0x10, 0xb, 0x60, 0x0, 0x5,
    0xf3, 0x34, 0x10, 0x5, 0xd0, 0x7, 0xa0, 0x0,
    0x0, 0x2e, 0xff, 0xe5, 0x0, 0x5d, 0x6, 0x60,
    0x0, 0x0, 0x0, 0x7, 0x10, 0x0, 0x5, 0xd0,
    0x6, 0xb9, 0x99, 0x99, 0x9b, 0xfb, 0x0, 0x0,
    0x5d, 0x0, 0x1, 0x60, 0x0, 0x0, 0xad, 0x0,
    0x0, 0x5, 0xd0, 0x0, 0x9, 0x10, 0x0, 0x3f,
    0x40, 0x0, 0x0, 0x5d, 0x0, 0x0, 0x3a, 0x0,
    0x1d, 0xa0, 0x0, 0x0, 0x5, 0xd0, 0x37, 0x0,
    0x8a, 0x1c, 0xb0, 0x0, 0x0, 0x0, 0x5e, 0x8a,
    0x0, 0x0, 0xce, 0xc0, 0x0, 0x0, 0x0, 0xa,
    0xf9, 0x0, 0x0, 0x5e, 0xcf, 0xa2, 0x0, 0x0,
    0x0, 0xb9, 0x0, 0x2, 0xba, 0x20, 0x4e, 0xfb,
    0x73, 0x0, 0x1, 0x0, 0x49, 0x93, 0x0, 0x0,
    0x6, 0xcf, 0xf6, 0x0, 0x0, 0x43, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x24, 0x0,

    /* U+8BC1 "证" */
    0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0x30, 0x0, 0x8, 0xe1, 0x5,
    0x99, 0x99, 0xad, 0x99, 0xab, 0xa0, 0x0, 0x1,
    0xf4, 0x0, 0x0, 0x0, 0x6e, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x6e, 0x0,
    0x0, 0x0, 0x9, 0x9a, 0xf4, 0x0, 0x0, 0x0,
    0x6e, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x24, 0x10, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xd0, 0x0, 0x6e, 0x10, 0x6e, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xd0, 0x0, 0x6d, 0x0, 0x6e, 0x0,
    0x3b, 0x10, 0x0, 0x5, 0xd0, 0x0, 0x6d, 0x0,
    0x6f, 0x99, 0xab, 0x80, 0x0, 0x5, 0xd0, 0x0,
    0x6d, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xd0, 0x0, 0x6d, 0x0, 0x6e, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xd0, 0x0, 0x6d, 0x0, 0x6e, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xd0, 0x38, 0x6d, 0x0,
    0x6e, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe8, 0xb0,
    0x6d, 0x0, 0x6e, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xfb, 0x0, 0x6d, 0x0, 0x6e, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xb0, 0x0, 0x6d, 0x0, 0x6e, 0x0,
    0xc, 0x90, 0x0, 0x3, 0x1, 0x99, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x92,

    /* U+8BEF "误" */
    0x0, 0x51, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0, 0x0, 0x1e, 0x50, 0x2, 0xe9, 0x99,
    0x99, 0x9a, 0xf8, 0x0, 0x0, 0x4, 0xf4, 0x2,
    0xf2, 0x0, 0x0, 0x2, 0xf3, 0x0, 0x0, 0x0,
    0xbb, 0x2, 0xf2, 0x0, 0x0, 0x2, 0xf2, 0x0,
    0x0, 0x0, 0x23, 0x2, 0xf2, 0x0, 0x0, 0x2,
    0xf2, 0x0, 0x2, 0x23, 0xa1, 0x2, 0xf2, 0x0,
    0x0, 0x2, 0xf2, 0x0, 0x6, 0x69, 0xf3, 0x2,
    0xfa, 0x99, 0x99, 0x9a, 0xf1, 0x0, 0x0, 0x5,
    0xe0, 0x1, 0x60, 0x0, 0x0, 0x0, 0x81, 0x0,
    0x0, 0x5, 0xe0, 0x6, 0x99, 0x99, 0x99, 0x9a,
    0xfd, 0x0, 0x0, 0x5, 0xe0, 0x0, 0x0, 0x4,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x0, 0x5, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xe0, 0x0, 0x0, 0x7, 0xf0, 0x0, 0x1d, 0x60,
    0x0, 0x5, 0xe0, 0x89, 0x99, 0x9d, 0xeb, 0x99,
    0x99, 0x91, 0x0, 0x5, 0xe0, 0x3, 0x0, 0xe,
    0x6a, 0x0, 0x0, 0x0, 0x0, 0x5, 0xe4, 0xa1,
    0x0, 0x6e, 0x5, 0xa0, 0x0, 0x0, 0x0, 0x9,
    0xfc, 0x10, 0x2, 0xe3, 0x0, 0xac, 0x20, 0x0,
    0x0, 0xc, 0xb0, 0x0, 0x3c, 0x30, 0x0, 0xb,
    0xf8, 0x10, 0x0, 0x2, 0x0, 0x18, 0x81, 0x0,
    0x0, 0x0, 0x9f, 0xd1, 0x0, 0x0, 0x0, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x20,

    /* U+8BF7 "请" */
    0x0, 0x43, 0x0, 0x0, 0x0, 0x1, 0xe6, 0x0,
    0x0, 0x0, 0x0, 0xd, 0x80, 0x0, 0x0, 0x1,
    0xf2, 0x0, 0x27, 0x0, 0x0, 0x3, 0xf4, 0x9,
    0x99, 0x9a, 0xfa, 0x99, 0xdf, 0x80, 0x0, 0x0,
    0xb5, 0x0, 0x0, 0x1, 0xf2, 0x0, 0x20, 0x0,
    0x0, 0x0, 0x10, 0x1, 0x44, 0x46, 0xf7, 0x45,
    0xf9, 0x0, 0x9, 0x9a, 0xf3, 0x0, 0x44, 0x45,
    0xf6, 0x44, 0x43, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0x0, 0x1, 0xf2, 0x0, 0x8, 0x20, 0x0, 0x5,
    0xe0, 0x49, 0x99, 0x9a, 0xea, 0x99, 0xae, 0xd1,
    0x0, 0x5, 0xe0, 0x0, 0x20, 0x0, 0x0, 0x0,
    0x50, 0x0, 0x0, 0x5, 0xe0, 0x0, 0xeb, 0x99,
    0x99, 0x9a, 0xfa, 0x0, 0x0, 0x5, 0xe0, 0x0,
    0xe5, 0x0, 0x0, 0x0, 0xf4, 0x0, 0x0, 0x5,
    0xe0, 0x0, 0xeb, 0x99, 0x99, 0x99, 0xf4, 0x0,
    0x0, 0x5, 0xe0, 0x0, 0xe5, 0x0, 0x0, 0x0,
    0xf4, 0x0, 0x0, 0x5, 0xe0, 0x53, 0xe5, 0x0,
    0x0, 0x0, 0xf4, 0x0, 0x0, 0x5, 0xe8, 0x70,
    0xeb, 0x99, 0x99, 0x99, 0xf4, 0x0, 0x0, 0x8,
    0xfa, 0x0, 0xe5, 0x0, 0x0, 0x0, 0xf4, 0x0,
    0x0, 0xc, 0xc0, 0x0, 0xe5, 0x0, 0x0, 0x0,
    0xf4, 0x0, 0x0, 0x4, 0x10, 0x0, 0xe5, 0x0,
    0x3, 0x9d, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd3, 0x0, 0x0, 0xd, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8C03 "调" */
    0x0, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x6, 0xd2, 0x0, 0x5d, 0x98, 0x88,
    0x88, 0x8b, 0xf4, 0x0, 0xa, 0xe0, 0x5, 0xe0,
    0x1, 0x63, 0x0, 0x5f, 0x10, 0x0, 0x1f, 0x30,
    0x5d, 0x0, 0x2f, 0x10, 0x4, 0xe0, 0x0, 0x1,
    0x20, 0x5, 0xd1, 0x24, 0xf2, 0xb5, 0x4e, 0x3,
    0x99, 0xea, 0x0, 0x5d, 0x27, 0x8f, 0x77, 0x54,
    0xe0, 0x0, 0xe, 0x50, 0x5, 0xd0, 0x2, 0xf0,
    0x0, 0x4e, 0x0, 0x0, 0xe5, 0x0, 0x5d, 0x0,
    0x2f, 0x6, 0x84, 0xe0, 0x0, 0xe, 0x50, 0x5,
    0xd6, 0x99, 0x99, 0x99, 0x6e, 0x0, 0x0, 0xe5,
    0x0, 0x6d, 0x2, 0x0, 0x6, 0x4, 0xe0, 0x0,
    0xe, 0x50, 0x6, 0xc1, 0xf9, 0x99, 0xf9, 0x4e,
    0x0, 0x0, 0xe5, 0x0, 0x8b, 0x1f, 0x0, 0xe,
    0x34, 0xe0, 0x0, 0xe, 0x50, 0x2a, 0x81, 0xf0,
    0x0, 0xe3, 0x4e, 0x0, 0x0, 0xe5, 0x85, 0xd5,
    0x1f, 0x0, 0xe, 0x34, 0xe0, 0x0, 0xf, 0xe7,
    0x2e, 0x1, 0xf9, 0x99, 0xf3, 0x4e, 0x0, 0x6,
    0xf8, 0x8, 0x70, 0x6, 0x0, 0x3, 0x4, 0xe0,
    0x0, 0x1a, 0x1, 0xc0, 0x0, 0x0, 0x0, 0x20,
    0x5e, 0x0, 0x0, 0x0, 0xb2, 0x0, 0x0, 0x0,
    0x3, 0xaf, 0xb0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x71, 0x0,

    /* U+8F93 "输" */
    0x0, 0x3, 0x20, 0x0, 0x0, 0x0, 0x81, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xb1, 0x0, 0x0, 0x8,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0xe, 0x40, 0x0,
    0x0, 0x3f, 0x76, 0x50, 0x0, 0x0, 0x0, 0x2f,
    0x10, 0xc4, 0x0, 0xd8, 0x0, 0x98, 0x0, 0x0,
    0x9, 0xce, 0x99, 0x98, 0xa, 0x90, 0x0, 0x4b,
    0xe6, 0x0, 0x0, 0xa8, 0x84, 0x0, 0xa7, 0x88,
    0x8b, 0xf5, 0x9f, 0xe2, 0x0, 0xe4, 0xf2, 0x19,
    0x30, 0x0, 0x0, 0x0, 0x4, 0x60, 0x3, 0xf0,
    0xf1, 0x0, 0x50, 0x1, 0x70, 0x0, 0x28, 0x30,
    0x9, 0xa0, 0xf3, 0xa2, 0xfa, 0x9b, 0xf4, 0xc4,
    0x3f, 0x0, 0x2d, 0xa9, 0xf9, 0x85, 0xf1, 0x3,
    0xe0, 0xf0, 0x3e, 0x0, 0x0, 0x1, 0xf1, 0x0,
    0xf9, 0x8a, 0xe0, 0xf0, 0x3e, 0x0, 0x0, 0x1,
    0xf1, 0x1, 0xf2, 0x4, 0xe0, 0xf0, 0x3e, 0x0,
    0x0, 0x1, 0xf8, 0x93, 0xf1, 0x3, 0xe0, 0xf0,
    0x3e, 0x0, 0x5, 0x9d, 0xf6, 0x0, 0xf9, 0x9a,
    0xe0, 0xf0, 0x3e, 0x0, 0xd, 0xe7, 0xf1, 0x0,
    0xf1, 0x3, 0xe0, 0xf0, 0x3e, 0x0, 0x3, 0x1,
    0xf1, 0x0, 0xf1, 0x3, 0xe0, 0x60, 0x3e, 0x0,
    0x0, 0x1, 0xf1, 0x0, 0xf1, 0x3, 0xe0, 0x0,
    0x3e, 0x0, 0x0, 0x1, 0xf1, 0x0, 0xf1, 0x5d,
    0xd0, 0x59, 0xcd, 0x0, 0x0, 0x1, 0xe0, 0x0,
    0xd0, 0x7, 0x50, 0x2, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8FBE "达" */
    0x0, 0x20, 0x0, 0x0, 0x0, 0x3, 0xa6, 0x0,
    0x0, 0x0, 0x0, 0x98, 0x0, 0x0, 0x0, 0x4,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0xd, 0xa0, 0x0,
    0x0, 0x4, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xf4, 0x0, 0x0, 0x4, 0xf1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc4, 0x0, 0x0, 0x5, 0xf1, 0x0,
    0x8, 0x20, 0x0, 0x0, 0x0, 0x59, 0x99, 0x9b,
    0xf9, 0x99, 0xae, 0xd1, 0x0, 0x1, 0xb1, 0x0,
    0x0, 0x7, 0xe0, 0x0, 0x0, 0x0, 0x8, 0x8a,
    0xfa, 0x0, 0x0, 0xa, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xf2, 0x0, 0x0, 0xc, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xf2, 0x0, 0x0, 0x1f,
    0x5b, 0x90, 0x0, 0x0, 0x0, 0x1, 0xf2, 0x0,
    0x0, 0x7e, 0x0, 0xbd, 0x20, 0x0, 0x0, 0x1,
    0xf2, 0x0, 0x1, 0xe5, 0x0, 0xc, 0xe2, 0x0,
    0x0, 0x1, 0xf2, 0x0, 0xc, 0x80, 0x0, 0x1,
    0xee, 0x10, 0x0, 0x1, 0xf2, 0x1, 0xb6, 0x0,
    0x0, 0x0, 0x3f, 0x80, 0x0, 0x4, 0xe7, 0x48,
    0x20, 0x0, 0x0, 0x0, 0x7, 0x90, 0x0, 0x6d,
    0x17, 0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xf2, 0x0, 0x6e, 0xb8, 0x55, 0x44, 0x44,
    0x45, 0x51, 0xb, 0x60, 0x0, 0x1, 0x7b, 0xef,
    0xff, 0xff, 0xff, 0x80, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+8FD4 "返" */
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x40, 0x0, 0x0, 0x98, 0x0, 0x0, 0x0, 0x0,
    0x47, 0xcf, 0xf8, 0x0, 0x0, 0xd, 0x90, 0x0,
    0xd8, 0xab, 0xa8, 0x64, 0x10, 0x0, 0x0, 0x5,
    0xf4, 0x0, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd4, 0x0, 0xe5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe7, 0x33,
    0x33, 0x37, 0xb0, 0x0, 0x0, 0x4, 0xa0, 0x0,
    0xf8, 0x55, 0x55, 0x5d, 0xd3, 0x0, 0x29, 0x9b,
    0xf6, 0x0, 0xf4, 0x0, 0x0, 0x1f, 0x60, 0x0,
    0x0, 0x5, 0xd0, 0x0, 0xf3, 0x84, 0x0, 0x7f,
    0x0, 0x0, 0x0, 0x5, 0xd0, 0x2, 0xf0, 0x8,
    0xb2, 0xe7, 0x0, 0x0, 0x0, 0x5, 0xd0, 0x5,
    0xd0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0x5,
    0xd0, 0x9, 0x70, 0x0, 0x5f, 0xf7, 0x0, 0x0,
    0x0, 0x5, 0xd0, 0xd, 0x10, 0x4, 0xd2, 0x7f,
    0x70, 0x0, 0x0, 0x5, 0xd0, 0x57, 0x0, 0x5c,
    0x10, 0x9, 0xf3, 0x0, 0x0, 0x7, 0xd6, 0x70,
    0x19, 0x70, 0x0, 0x0, 0xd9, 0x0, 0x0, 0x9c,
    0x8, 0x92, 0x60, 0x0, 0x0, 0x0, 0x23, 0x0,
    0xc, 0xe1, 0x0, 0x7f, 0xb8, 0x65, 0x44, 0x55,
    0x56, 0x60, 0xb, 0x50, 0x0, 0x1, 0x7b, 0xde,
    0xff, 0xff, 0xff, 0x40, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+91CD "重" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x7a,
    0x40, 0x0, 0x0, 0x0, 0x1, 0x35, 0x78, 0xbd,
    0xfe, 0xb9, 0x80, 0x0, 0x0, 0x27, 0x87, 0x66,
    0x59, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xe0, 0x0, 0x0, 0xb, 0x60,
    0x5, 0x99, 0x99, 0x99, 0x9b, 0xf9, 0x99, 0x99,
    0x9b, 0xb2, 0x0, 0x0, 0x0, 0x0, 0x6, 0xe0,
    0x0, 0x0, 0x10, 0x0, 0x0, 0x6, 0xc9, 0x88,
    0x8b, 0xf8, 0x88, 0x8b, 0xe4, 0x0, 0x0, 0x6,
    0xe0, 0x0, 0x6, 0xe0, 0x0, 0x6, 0xf1, 0x0,
    0x0, 0x6, 0xe0, 0x0, 0x6, 0xe0, 0x0, 0x6,
    0xe0, 0x0, 0x0, 0x6, 0xf9, 0x99, 0x9b, 0xf9,
    0x99, 0x9b, 0xe0, 0x0, 0x0, 0x6, 0xe0, 0x0,
    0x6, 0xe0, 0x0, 0x6, 0xe0, 0x0, 0x0, 0x6,
    0xe0, 0x0, 0x6, 0xe0, 0x0, 0x6, 0xe0, 0x0,
    0x0, 0x6, 0xf9, 0x99, 0x9b, 0xf9, 0x99, 0x9b,
    0xd0, 0x0, 0x0, 0x1, 0x20, 0x0, 0x6, 0xe0,
    0x0, 0x0, 0x62, 0x0, 0x0, 0x39, 0x99, 0x99,
    0x9b, 0xf9, 0x99, 0x99, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xe0, 0x0, 0x0,
    0x6, 0x70, 0x8, 0x88, 0x88, 0x88, 0x8b, 0xe8,
    0x88, 0x88, 0x9e, 0xe5,

    /* U+9519 "错" */
    0x0, 0x7, 0xa3, 0x0, 0x0, 0x58, 0x20, 0x38,
    0x30, 0x0, 0x0, 0xc, 0xb0, 0x0, 0x0, 0x8b,
    0x0, 0x4e, 0x0, 0x0, 0x0, 0x2f, 0x51, 0xc5,
    0x0, 0x8b, 0x0, 0x4e, 0x7, 0x0, 0x0, 0x7d,
    0x77, 0x76, 0x79, 0xde, 0x99, 0xbf, 0xbf, 0x90,
    0x0, 0xd3, 0x0, 0x0, 0x0, 0x8b, 0x0, 0x4e,
    0x0, 0x0, 0x5, 0x90, 0x0, 0x50, 0x0, 0x8b,
    0x0, 0x4e, 0x0, 0x0, 0xb, 0x99, 0x9b, 0xf6,
    0x0, 0x8b, 0x0, 0x4e, 0x3, 0x60, 0x53, 0x4,
    0xe0, 0x3, 0x99, 0xcd, 0x99, 0xbe, 0x9d, 0xe4,
    0x0, 0x4, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xe0, 0x62, 0xb, 0x54,
    0x44, 0x44, 0xb8, 0x0, 0x9, 0x9b, 0xf9, 0xdc,
    0xf, 0x85, 0x55, 0x55, 0xdb, 0x10, 0x0, 0x4,
    0xe0, 0x0, 0xf, 0x40, 0x0, 0x0, 0xb8, 0x0,
    0x0, 0x4, 0xe0, 0x0, 0xf, 0x40, 0x0, 0x0,
    0xb8, 0x0, 0x0, 0x4, 0xe0, 0x2, 0xf, 0xb9,
    0x99, 0x99, 0xe8, 0x0, 0x0, 0x4, 0xe0, 0x83,
    0xf, 0x40, 0x0, 0x0, 0xb8, 0x0, 0x0, 0x4,
    0xfb, 0x40, 0xf, 0x40, 0x0, 0x0, 0xb8, 0x0,
    0x0, 0x8, 0xf5, 0x0, 0xf, 0x40, 0x0, 0x0,
    0xc8, 0x0, 0x0, 0x5, 0x70, 0x0, 0xf, 0xa8,
    0x88, 0x88, 0xd6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+95ED "闭" */
    0x0, 0x57, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x90, 0x0, 0x1, 0xe6, 0x19, 0x99, 0x99,
    0x99, 0x9a, 0xf7, 0xe, 0x70, 0x20, 0x0, 0x0,
    0x53, 0x0, 0x3, 0xf0, 0xf, 0x30, 0x0, 0x0,
    0x1, 0xf4, 0x0, 0x3, 0xf0, 0xf, 0x30, 0x0,
    0x0, 0x1, 0xf2, 0x0, 0x3, 0xf0, 0xf, 0x30,
    0x0, 0x0, 0x1, 0xf2, 0x5d, 0x13, 0xf0, 0xf,
    0x32, 0x99, 0x99, 0x9f, 0xfa, 0x99, 0x53, 0xf0,
    0xf, 0x30, 0x0, 0x0, 0x5f, 0xf2, 0x0, 0x3,
    0xf0, 0xf, 0x30, 0x0, 0x0, 0xcb, 0xf2, 0x0,
    0x3, 0xf0, 0xf, 0x30, 0x0, 0x6, 0xe2, 0xf2,
    0x0, 0x3, 0xf0, 0xf, 0x30, 0x0, 0x1e, 0x41,
    0xf2, 0x0, 0x3, 0xf0, 0xf, 0x30, 0x0, 0xc6,
    0x1, 0xf2, 0x0, 0x3, 0xf0, 0xf, 0x30, 0x9,
    0x50, 0x1, 0xf2, 0x0, 0x3, 0xf0, 0xf, 0x30,
    0x82, 0x0, 0x1, 0xf2, 0x0, 0x3, 0xf0, 0xf,
    0x35, 0x0, 0x13, 0x23, 0xf2, 0x0, 0x3, 0xf0,
    0xf, 0x30, 0x0, 0x3, 0xaf, 0xe0, 0x0, 0x3,
    0xf0, 0xf, 0x30, 0x0, 0x0, 0x4, 0x0, 0x27,
    0xac, 0xf0, 0xe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5d, 0x60,

    /* U+95F4 "间" */
    0x5, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x9,
    0x20, 0x0, 0x1e, 0xa3, 0x99, 0x99, 0x99, 0x99,
    0xfb, 0xe, 0xa0, 0x6a, 0x0, 0x0, 0x0, 0x0,
    0xe, 0x50, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0xe5, 0xe, 0x50, 0xe, 0x98, 0x88, 0x9e,
    0x80, 0xe, 0x50, 0xe5, 0x0, 0xf4, 0x0, 0x0,
    0xe6, 0x0, 0xe5, 0xe, 0x50, 0xf, 0x30, 0x0,
    0xe, 0x50, 0xe, 0x50, 0xe5, 0x0, 0xf3, 0x0,
    0x0, 0xe5, 0x0, 0xe5, 0xe, 0x50, 0xf, 0xa9,
    0x99, 0x9f, 0x50, 0xe, 0x50, 0xe5, 0x0, 0xf3,
    0x0, 0x0, 0xe5, 0x0, 0xe5, 0xe, 0x50, 0xf,
    0x30, 0x0, 0xe, 0x50, 0xe, 0x50, 0xe5, 0x0,
    0xf3, 0x0, 0x0, 0xe5, 0x0, 0xe5, 0xe, 0x50,
    0xf, 0xb9, 0x99, 0x9f, 0x50, 0xe, 0x50, 0xe5,
    0x0, 0xf3, 0x0, 0x0, 0xe5, 0x0, 0xe5, 0xe,
    0x50, 0xf, 0x20, 0x0, 0x9, 0x10, 0xe, 0x50,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf5,
    0xe, 0x50, 0x0, 0x0, 0x0, 0x1, 0x6b, 0xff,
    0x40, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0x80, 0x0,

    /* U+96F6 "零" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x40, 0x0, 0x0, 0x9, 0x99, 0x99, 0x99, 0x99,
    0x99, 0x9e, 0xf5, 0x0, 0x0, 0x10, 0x0, 0x0,
    0xe, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x98,
    0x88, 0x88, 0x8f, 0xb8, 0x88, 0x88, 0x8b, 0xc1,
    0x0, 0xc2, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0,
    0x1d, 0x91, 0x8, 0xf0, 0x79, 0x99, 0xe, 0x52,
    0x88, 0x85, 0x3b, 0x0, 0xc, 0x60, 0x0, 0x0,
    0xe, 0x50, 0x0, 0x0, 0x20, 0x0, 0x0, 0x1,
    0x99, 0x99, 0xb, 0x32, 0x99, 0x98, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1b, 0xf7, 0x59,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xeb,
    0x58, 0x13, 0xda, 0x51, 0x0, 0x0, 0x0, 0x2,
    0xbc, 0x40, 0x7, 0xe0, 0x6, 0xdf, 0xda, 0x82,
    0x3, 0x99, 0x30, 0x0, 0x0, 0x80, 0x0, 0x97,
    0x9d, 0x80, 0x3, 0x0, 0x99, 0x99, 0x99, 0x99,
    0x9e, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xab, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7a, 0x84, 0x3b, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x7e, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x50, 0x0, 0x0, 0x0,

    /* U+9A6C "马" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x0,
    0x0, 0x2, 0x99, 0x99, 0x99, 0x99, 0x99, 0x9f,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0x50, 0x0, 0x0, 0x0, 0x8d, 0x20, 0x0,
    0x0, 0x1f, 0x30, 0x0, 0x0, 0x0, 0xaa, 0x0,
    0x0, 0x0, 0x3f, 0x10, 0x0, 0x0, 0x0, 0xc8,
    0x0, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x0, 0x0,
    0xe5, 0x0, 0x0, 0x0, 0x7e, 0x0, 0x0, 0x0,
    0x1, 0xf3, 0x0, 0x0, 0x0, 0x9c, 0x0, 0x0,
    0x0, 0x4, 0xf0, 0x0, 0x0, 0x0, 0xca, 0x0,
    0x50, 0x0, 0xb, 0xf9, 0x99, 0x99, 0x99, 0xdb,
    0x9b, 0xf7, 0x0, 0x1, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xe4, 0x6, 0xe0, 0x69, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x98, 0x8, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x89,
    0xde, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xd4, 0x0,

    /* U+9A8C "验" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x2,
    0xe8, 0x0, 0x0, 0x0, 0x5, 0x88, 0x89, 0xf4,
    0x0, 0x8, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xe2, 0x0, 0x1f, 0x58, 0x20, 0x0, 0x0,
    0x0, 0x21, 0x4, 0xd0, 0x0, 0x9b, 0x2, 0xc0,
    0x0, 0x0, 0x0, 0x8c, 0x15, 0xc0, 0x3, 0xe1,
    0x0, 0x8a, 0x0, 0x0, 0x0, 0x98, 0x6, 0xb0,
    0xc, 0x30, 0x0, 0xc, 0xb0, 0x0, 0x0, 0xb6,
    0x8, 0xa0, 0x94, 0x0, 0x0, 0x1, 0xed, 0x30,
    0x0, 0xd4, 0x9, 0x97, 0x30, 0x0, 0x0, 0x39,
    0x2d, 0xc1, 0x0, 0xe2, 0xb, 0x91, 0x7, 0x99,
    0x99, 0x99, 0x20, 0x10, 0x6, 0xfa, 0x9e, 0xfc,
    0x20, 0x4, 0x10, 0x1, 0xa5, 0x0, 0x0, 0x60,
    0x0, 0x98, 0x27, 0x2, 0xb0, 0x5, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0xa7, 0xd, 0x20, 0xd3, 0x9,
    0xc0, 0x0, 0x0, 0x1, 0x64, 0xb6, 0x8, 0x90,
    0xa9, 0xc, 0x60, 0x0, 0x19, 0xdd, 0x40, 0xd5,
    0x6, 0xe0, 0x8c, 0xf, 0x10, 0x0, 0xc, 0x70,
    0x0, 0xf3, 0x4, 0xf0, 0x59, 0x4a, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xf1, 0x1, 0x70, 0x0, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xe0, 0x0, 0x0,
    0x0, 0x80, 0xa, 0x30, 0x0, 0x19, 0xff, 0x95,
    0x99, 0x99, 0x99, 0xa9, 0x9b, 0xa0, 0x0, 0x0,
    0xca, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x15, 0xae, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7c, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xef, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x4, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x69, 0xff, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xfe, 0x95, 0x0, 0x8, 0xff,
    0x0, 0x0, 0xf, 0xff, 0xc7, 0x30, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0xf, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0xf, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x0, 0x0,
    0xf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0xf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0xf, 0xf8, 0x0, 0x0,
    0x1, 0x7b, 0xbd, 0xff, 0x0, 0x0, 0xf, 0xf8,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0x0, 0x13,
    0x2f, 0xf8, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0x2b, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xfc, 0xdf, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x6, 0xef, 0xff, 0xa1, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x7f, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x8a, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F008 "" */
    0xc4, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x4c, 0xfd, 0xcd, 0xff, 0xee, 0xee, 0xee,
    0xee, 0xff, 0xdc, 0xdf, 0xfa, 0x8a, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xb8, 0xaf, 0xf4, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x40, 0x4f,
    0xf4, 0x4, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x50, 0x4f, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xf7, 0x47, 0xfd, 0x77,
    0x77, 0x77, 0x77, 0xdf, 0x84, 0x7f, 0xf4, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x4f,
    0xf7, 0x47, 0xfd, 0x77, 0x77, 0x77, 0x77, 0xdf,
    0x84, 0x7f, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xf4, 0x4, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x50, 0x4f, 0xf4, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x40, 0x4f,
    0xfa, 0x8a, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb8, 0xaf, 0xfd, 0xcd, 0xff, 0xee, 0xee, 0xee,
    0xee, 0xff, 0xdc, 0xdf, 0xc4, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x4c,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xfa, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xf9, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xf9, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff,
    0xfa, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xf9, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xfa, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0x60, 0x6, 0xe4, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0x40, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0x60, 0x0, 0xff, 0xff,
    0xf4, 0x0, 0x4, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0x40, 0x4f, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5e,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x6, 0x70, 0x0, 0x0, 0x0, 0x18, 0x40, 0x8f,
    0xfb, 0x0, 0x0, 0x1, 0xdf, 0xf4, 0xff, 0xff,
    0xb0, 0x0, 0x1d, 0xff, 0xfb, 0x7f, 0xff, 0xfb,
    0x1, 0xdf, 0xff, 0xf4, 0x8, 0xff, 0xff, 0xbd,
    0xff, 0xff, 0x40, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x1d, 0xff,
    0xff, 0x48, 0xff, 0xff, 0xb0, 0xcf, 0xff, 0xf4,
    0x0, 0x8f, 0xff, 0xf9, 0xdf, 0xff, 0x40, 0x0,
    0x8, 0xff, 0xf9, 0x2e, 0xf4, 0x0, 0x0, 0x0,
    0x8f, 0xc0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x1,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x26, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x64, 0x0,
    0xdf, 0xf4, 0x0, 0x72, 0x0, 0x0, 0x0, 0xb,
    0xfe, 0x10, 0xdf, 0xf4, 0x9, 0xfe, 0x30, 0x0,
    0x0, 0xaf, 0xff, 0x50, 0xdf, 0xf4, 0xe, 0xff,
    0xe1, 0x0, 0x5, 0xff, 0xfb, 0x0, 0xdf, 0xf4,
    0x5, 0xff, 0xfb, 0x0, 0xd, 0xff, 0xb0, 0x0,
    0xdf, 0xf4, 0x0, 0x5f, 0xff, 0x40, 0x4f, 0xff,
    0x20, 0x0, 0xdf, 0xf4, 0x0, 0xb, 0xff, 0xa0,
    0x8f, 0xfb, 0x0, 0x0, 0xdf, 0xf4, 0x0, 0x4,
    0xff, 0xf0, 0xbf, 0xf7, 0x0, 0x0, 0xdf, 0xf4,
    0x0, 0x1, 0xff, 0xf1, 0xbf, 0xf6, 0x0, 0x0,
    0xdf, 0xf4, 0x0, 0x0, 0xff, 0xf2, 0xbf, 0xf7,
    0x0, 0x0, 0x8d, 0xc1, 0x0, 0x0, 0xff, 0xf1,
    0x8f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf0, 0x4f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xb0, 0xe, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x50, 0x6, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfc, 0x0,
    0x0, 0xaf, 0xff, 0xd5, 0x10, 0x3, 0x9f, 0xff,
    0xf2, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x9e, 0xff, 0xff, 0xb6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x2b, 0xff, 0xff, 0xb2, 0x0, 0x10, 0x0,
    0x0, 0x8f, 0x87, 0xff, 0xff, 0xff, 0xff, 0x79,
    0xf8, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x2f, 0xff,
    0xff, 0xff, 0xc7, 0x7c, 0xff, 0xff, 0xff, 0xf2,
    0x7, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x70, 0x0, 0x6f, 0xff, 0xf1, 0x0, 0x0,
    0x1f, 0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0x0, 0x0, 0xe, 0xff, 0xf7, 0x0, 0x0, 0x6f,
    0xff, 0xf1, 0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0,
    0x7, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x70, 0x2f, 0xff, 0xff, 0xff, 0xc7, 0x7c,
    0xff, 0xff, 0xff, 0xf2, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x8f, 0x97, 0xff, 0xff, 0xff, 0xff, 0x78,
    0xf8, 0x0, 0x0, 0x1, 0x0, 0x1b, 0xff, 0xff,
    0xb1, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x46, 0x10, 0x0,
    0x67, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfe, 0x20, 0xf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0x51, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xfc,
    0x8f, 0xff, 0x8f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xfa, 0x0, 0x4e, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf7, 0x8, 0xd3,
    0x2d, 0xff, 0xff, 0x10, 0x0, 0x0, 0xa, 0xff,
    0xf5, 0x1b, 0xff, 0xf5, 0xb, 0xff, 0xf4, 0x0,
    0x0, 0x1c, 0xff, 0xe2, 0x2d, 0xff, 0xff, 0xf7,
    0x8, 0xff, 0xf6, 0x0, 0x3e, 0xff, 0xc1, 0x4e,
    0xff, 0xff, 0xff, 0xfa, 0x5, 0xff, 0xf9, 0xe,
    0xff, 0x90, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x13, 0xef, 0xf6, 0x4f, 0x70, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x31, 0xcc, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0x80, 0x1, 0xef, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xf6, 0x0, 0xe,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0x60, 0x0, 0xef, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xf6, 0x0, 0xe, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0x40, 0x0, 0xcf, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x56, 0x65, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xee, 0xef, 0xff, 0xff, 0xfe, 0xee,
    0x70, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x7a, 0xaa, 0xaa, 0x91,
    0x4f, 0xf4, 0x19, 0xaa, 0xaa, 0xa7, 0xff, 0xff,
    0xff, 0xfd, 0x23, 0x32, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xf, 0x48, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xef, 0xff,
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41,

    /* U+F01C "" */
    0x0, 0x0, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x98, 0x88, 0x88, 0x88, 0x88, 0xdf,
    0xf3, 0x0, 0x0, 0x5, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x1, 0xef,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x80, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x30, 0x5f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfd,
    0xd, 0xff, 0x98, 0x88, 0x70, 0x0, 0x0, 0x3,
    0x88, 0x88, 0xdf, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0x7f,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x7, 0xba, 0x0, 0x0, 0x1, 0x7c, 0xff, 0xff,
    0xb5, 0x0, 0xb, 0xff, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xe5, 0xb, 0xff, 0x0, 0xa,
    0xff, 0xff, 0xdb, 0xbe, 0xff, 0xff, 0x9a, 0xff,
    0x0, 0x9f, 0xff, 0xa2, 0x0, 0x0, 0x3b, 0xff,
    0xff, 0xff, 0x5, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xd, 0xff, 0x60, 0x0,
    0x0, 0x7, 0xba, 0x9c, 0xff, 0xff, 0x3f, 0xfc,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0x7f, 0xf6, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x6f, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0xcf, 0xf3, 0xff, 0xff, 0xc9, 0xaa,
    0x70, 0x0, 0x0, 0x7, 0xff, 0xd0, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x50,
    0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x3b, 0xff,
    0xf9, 0x0, 0xff, 0xa9, 0xff, 0xff, 0xeb, 0xbd,
    0xff, 0xff, 0xa0, 0x0, 0xff, 0xb0, 0x5d, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff, 0xb0,
    0x0, 0x5b, 0xff, 0xff, 0xc8, 0x10, 0x0, 0x0,
    0xab, 0x70, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x2, 0xee, 0x0, 0x0, 0x0, 0x2e, 0xff, 0x0,
    0x0, 0x2, 0xef, 0xff, 0x47, 0x77, 0x7e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x9, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x0, 0x0, 0x0, 0x0, 0x89,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x89, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x73, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x1f, 0xf4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x5f, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x4f, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x1f, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x85, 0x4, 0x77, 0x77, 0xef, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xee, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7b, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x9, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xee, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0,
    0x0, 0x8, 0x70, 0x8, 0xfd, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0x0, 0x0, 0xef, 0xb0, 0xc,
    0xf6, 0x4, 0x77, 0x77, 0xef, 0xff, 0xf0, 0x0,
    0x2, 0xdf, 0x80, 0x3f, 0xd0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x74, 0x1, 0xff, 0x10, 0xcf,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xf4,
    0x8, 0xf7, 0x7, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x5f, 0xc0, 0x3f, 0xa0, 0x4f, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xff, 0x2,
    0xfb, 0x4, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x5f, 0xc0, 0x3f, 0xa0, 0x5f, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x1f, 0xf4, 0x8, 0xf7,
    0x7, 0xf6, 0xdf, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x74, 0x2, 0xff, 0x10, 0xcf, 0x30, 0x0, 0x0,
    0x9f, 0xff, 0xf0, 0x0, 0x2, 0xef, 0x80, 0x3f,
    0xd0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x0, 0x0,
    0xef, 0xb0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf0, 0x0, 0x8, 0x70, 0x8, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x89, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7b,
    0x20, 0x0, 0x0,

    /* U+F03E "" */
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xf6, 0x38, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x7f, 0xff, 0xff, 0xfb, 0xef,
    0xff, 0xff, 0xff, 0x80, 0x0, 0xcf, 0xff, 0xff,
    0x90, 0x3e, 0xff, 0xff, 0xff, 0xfa, 0x7c, 0xff,
    0xff, 0xf9, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0x90, 0x0, 0x0, 0x3e, 0xff,
    0xff, 0xff, 0x90, 0x6f, 0xf9, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xf9, 0x0, 0x6, 0x90, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xc8, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x8c, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x5e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F043 "" */
    0x0, 0x0, 0x0, 0x42, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x1, 0xef, 0xff, 0xff, 0xff, 0xfc, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xa6, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xef, 0x91, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xbf, 0xe0, 0x9f, 0xff, 0xff, 0xff, 0xf7,
    0x5f, 0xf9, 0x7, 0xcf, 0xff, 0xff, 0xf2, 0xc,
    0xff, 0xb3, 0x9, 0xff, 0xff, 0x80, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x15, 0x77,
    0x40, 0x0, 0x0,

    /* U+F048 "" */
    0x47, 0x60, 0x0, 0x0, 0x0, 0x16, 0x1b, 0xff,
    0x10, 0x0, 0x0, 0x2d, 0xfb, 0xbf, 0xf1, 0x0,
    0x0, 0x2e, 0xff, 0xcb, 0xff, 0x10, 0x0, 0x3e,
    0xff, 0xfc, 0xbf, 0xf1, 0x0, 0x4f, 0xff, 0xff,
    0xcb, 0xff, 0x10, 0x5f, 0xff, 0xff, 0xfc, 0xbf,
    0xf1, 0x6f, 0xff, 0xff, 0xff, 0xcb, 0xff, 0x9f,
    0xff, 0xff, 0xff, 0xfc, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcb,
    0xff, 0x4e, 0xff, 0xff, 0xff, 0xfc, 0xbf, 0xf1,
    0x2d, 0xff, 0xff, 0xff, 0xcb, 0xff, 0x10, 0x1c,
    0xff, 0xff, 0xfc, 0xbf, 0xf1, 0x0, 0xc, 0xff,
    0xff, 0xcb, 0xff, 0x10, 0x0, 0xb, 0xff, 0xfc,
    0xbf, 0xf1, 0x0, 0x0, 0xa, 0xff, 0xca, 0xff,
    0x10, 0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x6, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfd, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x20, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x50, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfd, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x6, 0x77, 0x77, 0x30, 0x0, 0x6, 0x77, 0x77,
    0x30, 0xbf, 0xff, 0xff, 0xf3, 0x0, 0xbf, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xf6, 0x0, 0xef, 0xff, 0xff,
    0xf6, 0x6f, 0xff, 0xff, 0xc1, 0x0, 0x6f, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04D "" */
    0x5, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x30, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F051 "" */
    0x5, 0x20, 0x0, 0x0, 0x0, 0x57, 0x66, 0xff,
    0x40, 0x0, 0x0, 0xc, 0xff, 0x8f, 0xff, 0x60,
    0x0, 0x0, 0xdf, 0xf8, 0xff, 0xff, 0x70, 0x0,
    0xd, 0xff, 0x8f, 0xff, 0xff, 0x80, 0x0, 0xdf,
    0xf8, 0xff, 0xff, 0xff, 0x90, 0xd, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xb0, 0xdf, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xcd, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0x5d, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0x40, 0xdf, 0xf8, 0xff, 0xff, 0xfe,
    0x30, 0xd, 0xff, 0x8f, 0xff, 0xfe, 0x20, 0x0,
    0xdf, 0xf8, 0xff, 0xfd, 0x20, 0x0, 0xd, 0xff,
    0x7f, 0xfd, 0x10, 0x0, 0x0, 0xdf, 0xf3, 0xfb,
    0x10, 0x0, 0x0, 0xc, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe2, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x40, 0x0, 0x0, 0x5f, 0xff, 0xe2, 0x0, 0x0,
    0x5f, 0xff, 0xe2, 0x0, 0x0, 0x5f, 0xff, 0xe3,
    0x0, 0x0, 0x5f, 0xff, 0xe3, 0x0, 0x0, 0x5f,
    0xff, 0xe3, 0x0, 0x0, 0x5f, 0xff, 0xe3, 0x0,
    0x0, 0xd, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdb, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x9, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf6, 0x0,
    0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0x0, 0x0, 0x9, 0xff, 0xfc,
    0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0x0, 0x2, 0xdb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x2, 0x55, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x37,
    0x77, 0x77, 0x8f, 0xff, 0xc7, 0x77, 0x77, 0x60,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x14, 0x44, 0x44, 0x5f, 0xff,
    0xb4, 0x44, 0x44, 0x30, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x21, 0x0,
    0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x49, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x2, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x10,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x6, 0xad, 0xff, 0xec, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xef, 0xff, 0xfc, 0x40, 0x0, 0x0, 0x0,
    0x3, 0xdf, 0xff, 0x92, 0x0, 0x5, 0xdf, 0xff,
    0x90, 0x0, 0x0, 0x4, 0xff, 0xff, 0x50, 0x2,
    0x52, 0x1, 0xcf, 0xff, 0xb0, 0x0, 0x3, 0xff,
    0xff, 0x80, 0x0, 0x7f, 0xf9, 0x1, 0xef, 0xff,
    0xb0, 0x1, 0xef, 0xff, 0xf0, 0x0, 0x8, 0xff,
    0xf7, 0x8, 0xff, 0xff, 0x80, 0xaf, 0xff, 0xfb,
    0x2, 0x25, 0xff, 0xff, 0xe0, 0x3f, 0xff, 0xff,
    0x2f, 0xff, 0xff, 0xa0, 0x7f, 0xff, 0xff, 0xff,
    0x2, 0xff, 0xff, 0xf7, 0x9f, 0xff, 0xfb, 0x5,
    0xff, 0xff, 0xff, 0xc0, 0x3f, 0xff, 0xff, 0x21,
    0xef, 0xff, 0xf0, 0xc, 0xff, 0xff, 0xf5, 0x7,
    0xff, 0xff, 0x80, 0x3, 0xff, 0xff, 0x80, 0x1a,
    0xff, 0xe5, 0x1, 0xef, 0xff, 0xb0, 0x0, 0x4,
    0xff, 0xff, 0x50, 0x0, 0x10, 0x1, 0xcf, 0xff,
    0xb0, 0x0, 0x0, 0x3, 0xdf, 0xff, 0x92, 0x0,
    0x5, 0xdf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xef, 0xff, 0xfc, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xad, 0xef, 0xec,
    0x83, 0x0, 0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x70, 0x4, 0x8c, 0xef, 0xed, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfb,
    0xef, 0xff, 0xfe, 0xff, 0xff, 0xe6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xc4, 0x0,
    0x4, 0xcf, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0x60, 0x3, 0x10, 0x9, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf9, 0x4f, 0xfa, 0x0, 0xcf, 0xff, 0xe1, 0x0,
    0x0, 0xb, 0xb0, 0x0, 0x3e, 0xff, 0xef, 0xff,
    0xa0, 0x4f, 0xff, 0xfb, 0x0, 0x0, 0x6f, 0xfd,
    0x30, 0x1, 0xcf, 0xff, 0xff, 0xf1, 0xf, 0xff,
    0xff, 0x50, 0x0, 0xbf, 0xff, 0xf6, 0x0, 0x8,
    0xff, 0xff, 0xf3, 0xe, 0xff, 0xff, 0xa0, 0x0,
    0x6f, 0xff, 0xff, 0x0, 0x0, 0x5f, 0xff, 0xf2,
    0xf, 0xff, 0xff, 0x50, 0x0, 0xc, 0xff, 0xff,
    0x40, 0x0, 0x2, 0xdf, 0xfe, 0x8f, 0xff, 0xfa,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xc0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xff,
    0xc4, 0x0, 0x0, 0x3, 0xef, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xef, 0xff, 0xfe, 0xe3,
    0x0, 0x1b, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9d, 0xef, 0xec, 0x20, 0x0, 0x8f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x60,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xa3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xa2, 0x24, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x90, 0x1, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xa0, 0x2, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xb0, 0x3, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xc0, 0x4, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xd0, 0x5, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xf9, 0x9c, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xf5, 0x2b, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0x90, 0x1, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x1, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xff,
    0xff, 0xff, 0xff, 0x90, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x1a, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x1, 0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x40,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x50, 0xef, 0xff,
    0xf6, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0x50, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xf7, 0x22, 0x23, 0xdf, 0xf8,
    0x9, 0xff, 0xf7, 0x2f, 0xff, 0x70, 0x0, 0x0,
    0x2e, 0xb0, 0x7f, 0xff, 0x90, 0xf, 0xf7, 0x0,
    0x0, 0x0, 0x3, 0x6, 0xff, 0xfa, 0x0, 0x7,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xfd, 0x3, 0x0, 0x7, 0x60, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xe1, 0x3f, 0x90, 0xf, 0xf8, 0x0,
    0x22, 0x23, 0xdf, 0xfe, 0x22, 0xef, 0xf7, 0x2f,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xf3, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0x50,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xf6, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x30, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x94, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xae, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xf9, 0x3, 0xef, 0xff, 0x50, 0x0, 0x0, 0xcf,
    0xff, 0x90, 0x0, 0x3e, 0xff, 0xf5, 0x0, 0xc,
    0xff, 0xf9, 0x0, 0x0, 0x3, 0xef, 0xff, 0x50,
    0xaf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x3e, 0xff,
    0xf2, 0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xe1, 0x6, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x29, 0x20,

    /* U+F078 "" */
    0x6, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29,
    0x20, 0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xe1, 0x9f, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xf2, 0xb, 0xff, 0xf9, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x50, 0x0, 0xbf, 0xff, 0x90,
    0x0, 0x3e, 0xff, 0xf5, 0x0, 0x0, 0xb, 0xff,
    0xf9, 0x3, 0xef, 0xff, 0x50, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xae, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x9c, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xd1, 0x0, 0x58, 0x88, 0x88, 0x88, 0x88, 0x81,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfd, 0x20, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xe2, 0x4e, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x8f, 0xfc, 0xff, 0xcf,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf6, 0x0,
    0x0, 0x7f, 0xc2, 0xff, 0x67, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x3, 0x1,
    0xff, 0x60, 0x30, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf6, 0x0, 0x0, 0x0, 0x1, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf6, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x3, 0xd7, 0x1f, 0xf6,
    0x3d, 0x70, 0x0, 0x1, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x7f, 0xf9, 0xef, 0xf0, 0x0,
    0x1, 0xff, 0xb8, 0x88, 0x88, 0x88, 0x32, 0xef,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x2e, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xe2, 0x2, 0xef, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x30, 0x0, 0x0,

    /* U+F07B "" */
    0x5e, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa8, 0x88, 0x88, 0x88, 0x60, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x5e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x11, 0x1b, 0xff, 0xff, 0x51, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x7a, 0xaa, 0xaa, 0x2b,
    0xff, 0xff, 0x42, 0xaa, 0xaa, 0xa7, 0xff, 0xff,
    0xff, 0x82, 0x67, 0x76, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x77, 0x77, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xf, 0x48, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xef, 0xff,
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xfe, 0xb5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x1, 0x30, 0x0, 0x0, 0x1d, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x29, 0xff, 0x70, 0x0, 0x3e, 0xff,
    0xff, 0x30, 0x0, 0x4, 0xbf, 0xff, 0xff, 0x40,
    0x7f, 0xff, 0xff, 0x50, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xfe, 0xef, 0xff, 0xff, 0x50, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x10, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xea, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x57, 0x64, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x25, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x4,
    0xaa, 0x50, 0x7f, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x8f, 0xff, 0xf5, 0xef, 0xd3, 0x7f, 0xf6, 0x0,
    0x8, 0xff, 0xff, 0xb0, 0xff, 0x80, 0xf, 0xf7,
    0x0, 0x8f, 0xff, 0xfb, 0x0, 0xdf, 0xe7, 0xaf,
    0xf5, 0x8, 0xff, 0xff, 0xb0, 0x0, 0x5f, 0xff,
    0xff, 0xfd, 0x9f, 0xff, 0xfb, 0x0, 0x0, 0x5,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x1, 0x5f, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x25, 0x9f, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xfb,
    0x6f, 0xff, 0xfd, 0x10, 0x0, 0xef, 0xd3, 0x7f,
    0xf5, 0x5, 0xff, 0xff, 0xd1, 0x0, 0xff, 0x80,
    0xf, 0xf7, 0x0, 0x5f, 0xff, 0xfd, 0x10, 0xdf,
    0xe7, 0xaf, 0xf5, 0x0, 0x5, 0xff, 0xff, 0xd1,
    0x5f, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x4f, 0xff,
    0xf4, 0x5, 0xef, 0xfb, 0x10, 0x0, 0x0, 0x1,
    0x66, 0x20, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x8, 0xbb, 0xbb, 0xbb, 0x50, 0x90,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0x81,
    0xfb, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x81, 0xff, 0xb0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0x81, 0xff, 0xf8, 0x8c, 0xc9, 0xf, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0xff, 0xfc, 0xf,
    0xff, 0xff, 0xff, 0xd5, 0x44, 0x43, 0xff, 0xfc,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xfe, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x58, 0x88, 0x88, 0x88,
    0x88, 0x87, 0x10, 0x0, 0x0,

    /* U+F0C7 "" */
    0x6, 0x77, 0x77, 0x77, 0x77, 0x77, 0x60, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xfc, 0x10, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xc0, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xf3, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf4, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf4,
    0xff, 0xd8, 0x88, 0x88, 0x88, 0x88, 0xef, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xe4, 0x2,
    0xcf, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x2f, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff,
    0x40, 0x0, 0xf, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x6f, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xfc, 0x8a, 0xff, 0xff, 0xff, 0xf4,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F0C9 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xac, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcd,
    0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd, 0xd5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xac, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xc4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0E0 "" */
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x30, 0xe3, 0x1b, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb1, 0x3e, 0xff, 0x70, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x7, 0xff, 0xff, 0xfb,
    0x13, 0xdf, 0xff, 0xff, 0xfd, 0x31, 0xbf, 0xff,
    0xff, 0xff, 0xe4, 0xa, 0xff, 0xff, 0xa0, 0x4e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x5d, 0xd5,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x20, 0x2, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x5e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F0E7 "" */
    0x0, 0x14, 0x44, 0x44, 0x41, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x40,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x6, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x14, 0x44, 0xbf, 0xfe, 0x44, 0x43, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xf9, 0x4f, 0xff, 0xff,
    0x40, 0x0, 0x0, 0xff, 0xff, 0xf8, 0x3f, 0xff,
    0xff, 0x50, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xa8, 0x88, 0x88, 0x20, 0x0, 0x0, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xf0, 0xcf, 0xff, 0xff, 0x51, 0xe2, 0x0,
    0xff, 0xff, 0xf0, 0xef, 0xff, 0xff, 0x51, 0xfe,
    0x20, 0xff, 0xff, 0xf0, 0xef, 0xff, 0xff, 0x51,
    0xff, 0xe2, 0xff, 0xff, 0xf0, 0xef, 0xff, 0xff,
    0x50, 0xbb, 0xb7, 0xff, 0xff, 0xf0, 0xef, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xff, 0xff, 0xf0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xf0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xf0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xf0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xcf, 0xff, 0xf0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x7b,
    0xbb, 0xbb, 0xbb, 0xbb, 0xb4,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xfb, 0x20, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x75, 0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xc8, 0x8f, 0xa8, 0xaf, 0x88, 0xbf, 0x88, 0xfb,
    0x88, 0xff, 0x8f, 0xf8, 0x0, 0xf4, 0x4, 0xf0,
    0x5, 0xe0, 0xe, 0x50, 0xf, 0xf8, 0xff, 0x80,
    0xf, 0x40, 0x4f, 0x0, 0x6f, 0x0, 0xf6, 0x0,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0x94,
    0x6f, 0x64, 0x8f, 0x44, 0xbb, 0x44, 0xff, 0xff,
    0x8f, 0xff, 0xf6, 0x2, 0xf2, 0x5, 0xf0, 0x8,
    0x80, 0xe, 0xff, 0xf8, 0xff, 0xff, 0x94, 0x6f,
    0x64, 0x8f, 0x44, 0xbb, 0x44, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0x80, 0xf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xf6, 0x0, 0xff, 0x8f, 0xf8,
    0x0, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xe, 0x50,
    0xf, 0xf8, 0xff, 0xc8, 0x8f, 0xa8, 0x88, 0x88,
    0x88, 0x88, 0xfb, 0x88, 0xff, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x56, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x7e, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x6, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x18, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x2, 0xac, 0xcc, 0xcc, 0xcd, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x24, 0x44, 0x44, 0x44, 0x30, 0x30, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xfc, 0xf, 0x60, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0xff, 0x60, 0xf, 0xff,
    0xff, 0xff, 0xfc, 0xf, 0xff, 0x60, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xfc, 0xb, 0xbb, 0xbb, 0xff, 0xff, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8b, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x80,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x43, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x8c, 0xff, 0xff, 0xff, 0xfc, 0x83, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x60, 0x0, 0x0, 0x3, 0xdf,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0x0, 0x8, 0xff, 0xff, 0xfb, 0x72, 0x0,
    0x0, 0x2, 0x7b, 0xff, 0xff, 0xf8, 0xa, 0xff,
    0xff, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xaf, 0xff, 0xfa, 0xbf, 0xfd, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xff, 0xb0,
    0xba, 0x10, 0x0, 0x5, 0x9d, 0xef, 0xed, 0x95,
    0x0, 0x0, 0x1a, 0xb0, 0x0, 0x0, 0x0, 0x6d,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xfa, 0x53, 0x23, 0x5a, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xb1, 0x0, 0x0,
    0x0, 0x1, 0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x9d, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x84, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc, 0xff, 0xff, 0xf8,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x5b, 0xff, 0xff, 0x84, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x8f, 0xff,
    0xf8, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8, 0xff, 0xff, 0x84, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F241 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x81, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x81, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F242 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x81, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x81, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x1f, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F243 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x81, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x81, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x1f, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F244 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7c,
    0xdf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfb, 0xbf, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0x70, 0xa, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x32, 0x0, 0x0, 0x9e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0x90, 0x1, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0x30, 0x0, 0xcf, 0xff, 0xf6, 0x3c, 0xf3,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x5f, 0xf9, 0x10,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0xcf, 0xff, 0xf6,
    0x33, 0x34, 0xed, 0x33, 0x33, 0x33, 0x33, 0x5f,
    0xfa, 0x10, 0x2d, 0xff, 0x90, 0x0, 0x0, 0x5f,
    0x30, 0x0, 0x0, 0x0, 0x1c, 0x30, 0x0, 0x0,
    0x32, 0x0, 0x0, 0x0, 0xd, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xf3, 0xa, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xce,
    0xae, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xbe, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x22, 0x20, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x34, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xbf, 0xff, 0xff, 0xe7, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xfa, 0xff, 0xff, 0xb0, 0x0,
    0x4, 0xff, 0xff, 0xf1, 0x9f, 0xff, 0xf8, 0x0,
    0xd, 0xff, 0xff, 0xf1, 0xa, 0xff, 0xff, 0x10,
    0x3f, 0xff, 0xff, 0xf1, 0x0, 0xbf, 0xff, 0x60,
    0x7f, 0xfd, 0x8f, 0xf1, 0x66, 0xc, 0xff, 0xa0,
    0xaf, 0xf8, 0x7, 0xf1, 0x6f, 0x13, 0xff, 0xd0,
    0xcf, 0xff, 0x70, 0x61, 0x53, 0x1e, 0xff, 0xf0,
    0xdf, 0xff, 0xf7, 0x0, 0x1, 0xdf, 0xff, 0xf0,
    0xef, 0xff, 0xff, 0x60, 0xc, 0xff, 0xff, 0xf0,
    0xef, 0xff, 0xff, 0x30, 0x7, 0xff, 0xff, 0xf0,
    0xdf, 0xff, 0xf3, 0x0, 0x10, 0x8f, 0xff, 0xf0,
    0xcf, 0xff, 0x30, 0xb1, 0x67, 0x9, 0xff, 0xf0,
    0x9f, 0xf6, 0xb, 0xf2, 0x6e, 0x2, 0xff, 0xd0,
    0x6f, 0xff, 0xcf, 0xf2, 0x52, 0x2e, 0xff, 0xa0,
    0x1f, 0xff, 0xff, 0xf2, 0x2, 0xef, 0xff, 0x50,
    0x9, 0xff, 0xff, 0xf2, 0x2e, 0xff, 0xfe, 0x0,
    0x0, 0xdf, 0xff, 0xf4, 0xef, 0xff, 0xf5, 0x0,
    0x0, 0x1a, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x27, 0xab, 0xb9, 0x50, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x2, 0xab, 0xbb, 0xb7, 0x0, 0x0,
    0x0, 0x57, 0x77, 0x7c, 0xff, 0xff, 0xff, 0x77,
    0x77, 0x72, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x20, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0xc, 0xff, 0x77, 0xff, 0x3b, 0xfe, 0x1e, 0xff,
    0x40, 0xc, 0xff, 0x66, 0xff, 0x2a, 0xfe, 0xe,
    0xff, 0x40, 0xc, 0xff, 0x66, 0xff, 0x2a, 0xfe,
    0xe, 0xff, 0x40, 0xc, 0xff, 0x66, 0xff, 0x2a,
    0xfe, 0xe, 0xff, 0x40, 0xc, 0xff, 0x66, 0xff,
    0x2a, 0xfe, 0xe, 0xff, 0x40, 0xc, 0xff, 0x66,
    0xff, 0x2a, 0xfe, 0xe, 0xff, 0x40, 0xc, 0xff,
    0x66, 0xff, 0x2a, 0xfe, 0xe, 0xff, 0x40, 0xc,
    0xff, 0x66, 0xff, 0x2a, 0xfe, 0xe, 0xff, 0x40,
    0xc, 0xff, 0x66, 0xff, 0x2a, 0xfe, 0xe, 0xff,
    0x40, 0xc, 0xff, 0x77, 0xff, 0x3b, 0xfe, 0x1e,
    0xff, 0x40, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x57, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x72, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x64, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x90, 0x8f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xb0, 0x8f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xb0, 0x8f,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff,
    0xff, 0xb0, 0x8e, 0x10, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0xff, 0xff, 0xb0, 0x10, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x1, 0xef, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0x75, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x20, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0x85, 0xff, 0xff, 0x58, 0xff,
    0xff, 0xff, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xb0,
    0x4, 0xff, 0x40, 0xb, 0xff, 0xff, 0xf0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x4, 0x40, 0x4,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x0,
    0xef, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x40, 0x4,
    0x40, 0x4, 0xff, 0xff, 0xff, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xb0, 0x4, 0xff, 0x40, 0xb, 0xff,
    0xff, 0xf0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x85,
    0xff, 0xff, 0x58, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x20,

    /* U+F7C2 "" */
    0x0, 0x0, 0x28, 0x88, 0x88, 0x88, 0x73, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x1d,
    0xf6, 0xe, 0x50, 0xd6, 0x8, 0xff, 0x1d, 0xff,
    0x60, 0xe5, 0xd, 0x60, 0x8f, 0xfc, 0xff, 0xf6,
    0xe, 0x50, 0xd6, 0x8, 0xff, 0xff, 0xff, 0x60,
    0xe5, 0xd, 0x60, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x7, 0xab,
    0xbb, 0xbb, 0xbb, 0xbb, 0xa6, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x10,
    0x0, 0xa, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf1, 0x0, 0xb, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x10, 0xc, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf1, 0x1c,
    0xff, 0xff, 0xcb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf,
    0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x9f,
    0xff, 0xf9, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x40, 0x0, 0x8f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+FF01 "！" */
    0xa, 0x42, 0xfa, 0x1f, 0x90, 0xf8, 0xf, 0x70,
    0xe5, 0xc, 0x40, 0xb3, 0xa, 0x20, 0x91, 0x8,
    0x10, 0x20, 0x0, 0x0, 0x0, 0x2f, 0xa1, 0xd8,

    /* U+FF1A "：" */
    0x0, 0x5, 0xf9, 0x6f, 0xb0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf8, 0x6f,
    0xb0, 0x30
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 82, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 99, .box_w = 4, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 30, .adv_w = 120, .box_w = 6, .box_h = 7, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 51, .adv_w = 184, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 141, .adv_w = 176, .box_w = 9, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 236, .adv_w = 295, .box_w = 18, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 380, .adv_w = 249, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 493, .adv_w = 63, .box_w = 2, .box_h = 7, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 500, .adv_w = 116, .box_w = 6, .box_h = 22, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 566, .adv_w = 116, .box_w = 6, .box_h = 22, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 632, .adv_w = 153, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 677, .adv_w = 186, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 743, .adv_w = 105, .box_w = 4, .box_h = 6, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 755, .adv_w = 111, .box_w = 7, .box_h = 2, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 762, .adv_w = 105, .box_w = 3, .box_h = 3, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 767, .adv_w = 113, .box_w = 7, .box_h = 20, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 837, .adv_w = 178, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 920, .adv_w = 151, .box_w = 8, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 984, .adv_w = 179, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1059, .adv_w = 178, .box_w = 10, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1134, .adv_w = 178, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1217, .adv_w = 179, .box_w = 11, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1300, .adv_w = 179, .box_w = 10, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1385, .adv_w = 176, .box_w = 9, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1453, .adv_w = 179, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1528, .adv_w = 180, .box_w = 10, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1608, .adv_w = 105, .box_w = 3, .box_h = 11, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1625, .adv_w = 105, .box_w = 4, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 1653, .adv_w = 186, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 1713, .adv_w = 186, .box_w = 11, .box_h = 6, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 1746, .adv_w = 186, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 1806, .adv_w = 138, .box_w = 7, .box_h = 16, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1862, .adv_w = 292, .box_w = 18, .box_h = 18, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 2024, .adv_w = 230, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2137, .adv_w = 214, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2227, .adv_w = 221, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2317, .adv_w = 246, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2422, .adv_w = 209, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2512, .adv_w = 201, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2595, .adv_w = 237, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2700, .adv_w = 273, .box_w = 16, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2820, .adv_w = 130, .box_w = 7, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2873, .adv_w = 129, .box_w = 9, .box_h = 18, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 2954, .adv_w = 234, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3059, .adv_w = 200, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3142, .adv_w = 312, .box_w = 19, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3285, .adv_w = 255, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3405, .adv_w = 245, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3510, .adv_w = 205, .box_w = 12, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3600, .adv_w = 245, .box_w = 14, .box_h = 20, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 3740, .adv_w = 229, .box_w = 13, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3838, .adv_w = 181, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3913, .adv_w = 211, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4011, .adv_w = 255, .box_w = 16, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4131, .adv_w = 228, .box_w = 15, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4244, .adv_w = 337, .box_w = 21, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4402, .adv_w = 224, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4507, .adv_w = 220, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4612, .adv_w = 194, .box_w = 12, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4702, .adv_w = 110, .box_w = 5, .box_h = 20, .ofs_x = 2, .ofs_y = -4},
    {.bitmap_index = 4752, .adv_w = 113, .box_w = 7, .box_h = 20, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4822, .adv_w = 110, .box_w = 5, .box_h = 20, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 4872, .adv_w = 186, .box_w = 9, .box_h = 7, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 4904, .adv_w = 180, .box_w = 13, .box_h = 1, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4911, .adv_w = 139, .box_w = 5, .box_h = 5, .ofs_x = 1, .ofs_y = 12},
    {.bitmap_index = 4924, .adv_w = 178, .box_w = 10, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4979, .adv_w = 204, .box_w = 12, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5081, .adv_w = 172, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5136, .adv_w = 201, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5247, .adv_w = 175, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5308, .adv_w = 124, .box_w = 10, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5393, .adv_w = 181, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 5481, .adv_w = 212, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5592, .adv_w = 106, .box_w = 6, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5640, .adv_w = 100, .box_w = 7, .box_h = 21, .ofs_x = -2, .ofs_y = -5},
    {.bitmap_index = 5714, .adv_w = 194, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5825, .adv_w = 107, .box_w = 7, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5885, .adv_w = 312, .box_w = 19, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5990, .adv_w = 212, .box_w = 13, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6062, .adv_w = 191, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6123, .adv_w = 204, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 6219, .adv_w = 194, .box_w = 12, .box_h = 16, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 6315, .adv_w = 148, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6370, .adv_w = 151, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6414, .adv_w = 117, .box_w = 8, .box_h = 14, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6470, .adv_w = 209, .box_w = 13, .box_h = 12, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6548, .adv_w = 175, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6614, .adv_w = 268, .box_w = 18, .box_h = 11, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6713, .adv_w = 180, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6779, .adv_w = 177, .box_w = 13, .box_h = 16, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 6883, .adv_w = 159, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6938, .adv_w = 120, .box_w = 7, .box_h = 20, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 7008, .adv_w = 101, .box_w = 2, .box_h = 23, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 7031, .adv_w = 120, .box_w = 7, .box_h = 20, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 7101, .adv_w = 186, .box_w = 10, .box_h = 3, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 7116, .adv_w = 320, .box_w = 17, .box_h = 17, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 7261, .adv_w = 320, .box_w = 19, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7404, .adv_w = 320, .box_w = 20, .box_h = 3, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 7434, .adv_w = 320, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7624, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7824, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8014, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 8214, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8404, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8594, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8784, .adv_w = 320, .box_w = 18, .box_h = 18, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 8946, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9146, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9336, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9526, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9716, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9906, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 10087, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10277, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10458, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 10648, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10829, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 11010, .adv_w = 320, .box_w = 17, .box_h = 18, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 11163, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 11344, .adv_w = 320, .box_w = 21, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11544, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11734, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11924, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12124, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12324, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 12514, .adv_w = 320, .box_w = 19, .box_h = 20, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 12704, .adv_w = 320, .box_w = 17, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 12866, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13047, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13237, .adv_w = 320, .box_w = 21, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13437, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13627, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13827, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14017, .adv_w = 320, .box_w = 21, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14227, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 14427, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14617, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14807, .adv_w = 320, .box_w = 13, .box_h = 17, .ofs_x = 4, .ofs_y = -1},
    {.bitmap_index = 14918, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 15099, .adv_w = 320, .box_w = 20, .box_h = 18, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 15279, .adv_w = 320, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15432, .adv_w = 320, .box_w = 21, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15632, .adv_w = 320, .box_w = 21, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 15832, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16022, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16222, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16412, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 16593, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16774, .adv_w = 320, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 16964, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 17154, .adv_w = 320, .box_w = 21, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 17364, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 17554, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 17744, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 17934, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 18124, .adv_w = 320, .box_w = 21, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 18324, .adv_w = 320, .box_w = 21, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 18534, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 18724, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 18914, .adv_w = 320, .box_w = 20, .box_h = 18, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 19094, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19284, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19474, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19674, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 19864, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 20054, .adv_w = 320, .box_w = 20, .box_h = 18, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 20234, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 20415, .adv_w = 320, .box_w = 20, .box_h = 18, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 20595, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 20785, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 20985, .adv_w = 320, .box_w = 19, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 21166, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 21366, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 21556, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 21746, .adv_w = 320, .box_w = 20, .box_h = 18, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 21926, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 22116, .adv_w = 320, .box_w = 18, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 22287, .adv_w = 320, .box_w = 17, .box_h = 19, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 22449, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 22639, .adv_w = 320, .box_w = 18, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 22810, .adv_w = 320, .box_w = 20, .box_h = 20, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 23010, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 23220, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 23370, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 23560, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 23710, .adv_w = 220, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 23815, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 24025, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 24235, .adv_w = 360, .box_w = 23, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 24454, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 24664, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 24837, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 25047, .adv_w = 160, .box_w = 10, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 25127, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 25247, .adv_w = 360, .box_w = 23, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 25466, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 25616, .adv_w = 220, .box_w = 14, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 25763, .adv_w = 280, .box_w = 13, .box_h = 19, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 25887, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 26076, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 26247, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 26418, .adv_w = 280, .box_w = 13, .box_h = 19, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 26542, .adv_w = 280, .box_w = 19, .box_h = 19, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 26723, .adv_w = 200, .box_w = 11, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 26828, .adv_w = 200, .box_w = 11, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 26933, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 27104, .adv_w = 280, .box_w = 18, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 27149, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 27322, .adv_w = 400, .box_w = 26, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 27595, .adv_w = 360, .box_w = 24, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 27847, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 28037, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 28136, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 28235, .adv_w = 400, .box_w = 26, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 28443, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 28593, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 28803, .adv_w = 320, .box_w = 21, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 29024, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 29195, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 29384, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 29555, .adv_w = 280, .box_w = 18, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 29708, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 29858, .adv_w = 200, .box_w = 14, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 30005, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 30194, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 30383, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 30556, .adv_w = 320, .box_w = 22, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 30787, .adv_w = 240, .box_w = 15, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 30945, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 31183, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 31346, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 31509, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 31672, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 31835, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 31998, .adv_w = 400, .box_w = 26, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 32219, .adv_w = 280, .box_w = 16, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 32387, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 32576, .adv_w = 320, .box_w = 21, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 32797, .adv_w = 400, .box_w = 25, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 32985, .adv_w = 240, .box_w = 15, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 33143, .adv_w = 322, .box_w = 21, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 33280, .adv_w = 320, .box_w = 3, .box_h = 16, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 33304, .adv_w = 320, .box_w = 3, .box_h = 12, .ofs_x = 3, .ofs_y = 0}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0xa02, 0x2cfd, 0x2dab, 0x2de7, 0x2e50, 0x2ede, 0x2f39,
    0x3062, 0x3070, 0x30f7, 0x3103, 0x314a, 0x3213, 0x3252, 0x327f,
    0x32ea, 0x32ec, 0x332c, 0x3335, 0x3389, 0x3484, 0x35db, 0x380a,
    0x3813, 0x3a97, 0x3ac3, 0x3d71, 0x3da3, 0x3dfd, 0x3e0c, 0x3e50,
    0x3e52, 0x3f5f, 0x3f6c, 0x4148, 0x41a2, 0x41fe, 0x4206, 0x426b,
    0x42cd, 0x446d, 0x44e2, 0x44f3, 0x453b, 0x4605, 0x4704, 0x471e,
    0x47bd, 0x4b11, 0x4b1f, 0x4b24, 0x4bf2, 0x4c48, 0x4f6c, 0x50c0,
    0x56fe, 0x5768, 0x576b, 0x57b0, 0x5837, 0x58cf, 0x5bf8, 0x5ddc,
    0x5e6b, 0x617f, 0x62d9, 0x6765, 0x6a63, 0x6aa1, 0x6aad, 0x6abb,
    0x6abe, 0x6aec, 0x6af4, 0x6b00, 0x6e90, 0x6ebb, 0x6ed1, 0x70ca,
    0x7416, 0x74ea, 0x74f1, 0x75f3, 0x7969, 0x7989, 0xcefe, 0xcf05,
    0xcf08, 0xcf09, 0xcf0a, 0xcf0e, 0xcf10, 0xcf12, 0xcf16, 0xcf19,
    0xcf1e, 0xcf23, 0xcf24, 0xcf25, 0xcf3b, 0xcf40, 0xcf45, 0xcf48,
    0xcf49, 0xcf4a, 0xcf4e, 0xcf4f, 0xcf50, 0xcf51, 0xcf64, 0xcf65,
    0xcf6b, 0xcf6d, 0xcf6e, 0xcf71, 0xcf74, 0xcf75, 0xcf76, 0xcf78,
    0xcf90, 0xcf92, 0xcfc1, 0xcfc2, 0xcfc4, 0xcfc6, 0xcfdd, 0xcfe4,
    0xcfe7, 0xcff0, 0xd019, 0xd021, 0xd058, 0xd0e8, 0xd13d, 0xd13e,
    0xd13f, 0xd140, 0xd141, 0xd184, 0xd190, 0xd1ea, 0xd201, 0xd457,
    0xd6bf, 0xd79f, 0xddfe, 0xde17
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 8451, .range_length = 56856, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 148, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/


/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 0, 1, 2, 0, 0, 3,
    1, 4, 5, 6, 0, 7, 8, 7,
    9, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 10, 10, 0, 0, 0,
    11, 12, 13, 14, 15, 16, 17, 18,
    19, 20, 20, 21, 22, 23, 20, 24,
    25, 26, 25, 27, 28, 29, 30, 31,
    32, 33, 34, 35, 4, 36, 5, 0,
    37, 0, 38, 39, 40, 41, 42, 43,
    44, 45, 46, 47, 48, 41, 45, 45,
    39, 39, 49, 50, 51, 52, 53, 54,
    54, 55, 54, 56, 4, 0, 5, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 3, 0, 0, 4,
    2, 5, 6, 7, 0, 8, 9, 10,
    11, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 12, 13, 0, 0, 0,
    14, 15, 16, 17, 18, 17, 17, 17,
    18, 17, 17, 19, 17, 17, 20, 20,
    18, 17, 18, 17, 21, 22, 23, 24,
    25, 26, 27, 28, 5, 29, 6, 0,
    30, 0, 31, 32, 33, 33, 33, 34,
    35, 32, 36, 37, 32, 32, 38, 38,
    33, 39, 33, 38, 40, 41, 42, 43,
    43, 44, 45, 46, 5, 0, 6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 0, 0, 0, 0, 0, 0, -36,
    0, -36, -28, 0, 0, 0, 0, -43,
    0, -9, -3, 0, -3, 10, -1, 0,
    0, 0, 0, 0, 0, 0, -10, 0,
    -14, -2, -18, 0, 0, -3, -6, -10,
    0, 0, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -10, 0, 0, 0, 0, -13, 0, 4,
    0, 0, 0, 0, 0, -7, -7, 0,
    -2, 0, 0, 0, 0, 0, 0, -2,
    -2, 0, 0, 0, 0, 0, 3, 0,
    2, 0, 2, 0, 0, 0, 0, 0,
    0, -7, 0, -2, -8, -2, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    0, -19, -10, -22, -20, 0, -20, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, -7, -17, 4,
    0, 0, -14, 0, 0, -14, -14, 0,
    0, -10, 0, -7, 12, 0, -3, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -10, -2, 0, 0, 34, -5,
    0, -7, -3, -7, -7, -3, 5, -3,
    0, 0, 0, 0, 0, -17, 0, -14,
    0, -14, 0, 0, 0, 0, 0, -10,
    0, 0, 0, 0, 0, -7, -7, -17,
    -14, -7, -14, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -35, 0, -35,
    0, 0, 0, 0, 0, -39, 0, -3,
    -7, 0, 0, 7, 0, 0, 0, -2,
    0, 0, 0, 0, -13, 0, -9, -3,
    -15, 0, 0, -2, -3, -8, 0, 0,
    0, 0, 0, -4, 0, -28, 0, 0,
    -17, -15, -45, 0, -14, 0, 0, 0,
    0, 0, 0, 0, 0, -19, 0, 0,
    0, -28, -21, -46, -41, 0, -41, 0,
    -45, 0, -7, 0, -10, 0, -7, 0,
    3, 0, -10, -5, -14, -14, -31, 0,
    -14, -7, 0, 0, 0, 0, 0, -14,
    0, -17, 0, -17, -10, 0, 0, 0,
    0, -10, 0, 3, -7, -3, 0, -14,
    -3, -27, -24, -14, -24, -3, -14, 0,
    0, -1, 3, -2, 2, 0, 0, 0,
    0, 0, 0, 0, -2, -7, -3, 0,
    0, 0, -7, 0, 0, 0, 0, -47,
    -21, -47, -26, 0, 0, 0, 0, -24,
    0, 0, 0, 0, 0, 2, 0, 4,
    4, 0, 0, 0, 0, 0, -11, 0,
    -10, 0, -11, 0, 0, -6, -6, -7,
    0, -4, 0, -3, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -2, -3, -10, -10, -2,
    -17, 0, 0, 0, 0, 0, -1, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -11, 0, -39, 0, -39, -24, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -5, 0, -5, 0, 0, 0, 0,
    0, -2, 0, 2, 0, 0, 0, 0,
    0, -1, -1, -1, -2, 0, 0, -28,
    0, -4, 0, -2, -3, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 2, 0,
    0, -39, 0, 0, -6, -10, -37, 0,
    -11, 0, 0, 0, 0, 0, 0, 3,
    -3, -12, -4, -2, 0, -20, -23, -42,
    -35, 0, -31, 0, -21, 0, -1, 0,
    -6, 0, -1, 0, -3, 0, -10, 0,
    -7, -7, -18, 0, -15, 0, 0, -7,
    0, 0, 0, -5, -3, -9, 0, -9,
    -6, 0, 0, 0, 0, -10, -9, -2,
    -4, -5, 0, -7, -10, -17, -13, -10,
    -17, -2, -5, -9, 0, -3, 0, -5,
    0, 0, 0, 0, -3, 0, 0, -5,
    -6, -3, -3, 0, 0, 7, 0, 0,
    0, 1, 1, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, 0, 0, 0,
    0, 0, -3, -3, -3, 0, -7, 0,
    0, -2, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, -3, 0, 0,
    0, 0, 0, -1, 0, 0, 0, -7,
    0, -14, 1, -14, -10, 0, 0, 0,
    5, -18, -9, 3, -3, -7, 0, -6,
    -7, -15, -16, -14, -21, -3, -5, -24,
    0, -3, 0, 0, -3, 0, 0, 0,
    -3, -3, 0, 0, 0, -3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -5, -5,
    -5, 0, -5, 0, 0, 0, 0, 0,
    0, 0, -2, 0, 0, 0, -6, 0,
    0, -3, -6, 0, -7, 0, 0, 4,
    0, -3, -3, 0, 3, -33, -7, -33,
    -21, -2, -2, 0, -7, -25, -3, -3,
    0, 0, 0, 3, 0, 0, 0, 0,
    0, 0, 4, -42, -14, -1, -15, -10,
    -21, -3, 0, -7, -14, -15, 0, -7,
    -7, -10, -7, -10, 0, 0, 0, 0,
    3, -2, 0, -3, 7, -3, -5, 0,
    0, 0, 4, -3, -3, 3, 0, 0,
    0, -3, -5, -12, -12, -6, -15, -3,
    0, -13, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -7, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -7, 0, -8, -6, -6, 0, 0, 0,
    -11, -7, -3, -8, -13, 0, -10, 0,
    0, 0, 0, 0, 0, 0, 0, -18,
    -3, -18, -10, -7, -7, 0, -4, -13,
    0, -3, 0, 0, -3, 0, 0, 0,
    0, 0, 0, 0, 0, -25, -8, 0,
    -8, -13, -10, -3, -3, -9, -13, -10,
    -6, -10, -10, -7, -10, -7, 0, 0,
    0, 0, 0, 0, 0, 0, -10, 0,
    0, 0, 0, 0, 0, 3, 0, -10,
    -5, 0, 0, 0, -3, -3, -3, 0,
    -3, 3, 0, 0, -3, 0, -7, 0,
    -3, 0, 0, 0, -10, 0, -7, -7,
    -19, 0, -17, 0, 0, -45, 0, 0,
    0, -9, -35, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, -5, -6, -2,
    -5, -25, -15, -37, -32, -5, -35, -5,
    -19, 0, 0, 0, 0, -2, 0, 0,
    0, 0, -6, 0, -3, -7, -13, 0,
    -10, 0, 0, 0, 0, 0, 0, 0,
    0, -10, -1, -10, -11, -4, -4, 0,
    0, -12, -3, -5, 0, 0, -3, 0,
    -3, 0, 0, -3, 0, -1, 0, -19,
    -4, 0, -4, -7, -7, 0, 0, -3,
    -7, -4, -3, -7, -7, -6, -7, -7,
    0, -9, 0, 0, 0, -7, -3, -14,
    3, -14, -7, 0, 0, 0, 7, -12,
    -7, 3, -3, -5, 0, -3, -6, -12,
    -9, -10, -14, 0, -2, -19, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 3,
    0, -2, 0, -3, 7, -36, -2, -36,
    -17, 3, 3, 0, 0, -21, -3, 0,
    -3, 0, 2, 3, -7, -3, -3, -7,
    -7, 3, 3, -50, -2, -1, -7, 0,
    -7, 0, 0, -2, 0, -1, 5, 0,
    3, 0, 5, 0, 0, -14, 0, 0,
    0, 0, -4, 0, -5, 0, 0, 0,
    0, 0, 0, 3, 0, -6, -3, 0,
    0, -10, -11, -19, -17, 2, -17, 2,
    -7, 6, 0, 0, -3, 0, 0, 0,
    0, 0, -4, 0, -3, -4, -7, 0,
    -7, 0, 0, 0, 0, 0, 0, -3,
    0, -7, 0, -7, -5, 0, 0, 0,
    2, -6, -2, 0, -3, -2, -3, -3,
    -9, -9, -9, -3, -10, 0, 0, -14,
    0, -2, 0, -5, -2, 0, 0, 0,
    -3, 0, -3, -3, -6, -3, -6, 0,
    0, 10, 0, 0, -7, 0, 7, -28,
    -14, -28, -16, -2, -2, 0, -7, -22,
    0, -3, 0, 0, 0, 3, -3, 0,
    0, -1, 0, 2, 9, -28, -14, 0,
    -23, -3, -14, 0, 0, -10, -23, -17,
    0, -10, -14, -12, -14, -21, 0, 0,
    0, -4, -7, -3, 0, -28, -4, -28,
    -19, -4, -4, 0, 0, -25, -7, -9,
    -5, -3, -6, -3, -7, -6, -2, -3,
    0, -3, 0, -29, -14, -2, -10, -7,
    -14, -6, -3, -9, -15, -14, -7, -10,
    -8, -11, -10, -12, 0, 0, 0, -13,
    -17, 0, 0, -53, -26, -53, -30, -14,
    -14, 0, -17, -39, 0, -15, 0, 0,
    -7, 0, -3, 0, 0, 0, 0, -3,
    4, -55, -28, -2, -25, -17, -29, -10,
    -7, -20, -25, -29, -10, -21, -14, -24,
    -21, -22, 0, 0, 0, -11, -14, 0,
    0, -43, -22, -43, -31, -14, -14, 0,
    -17, -36, 0, -13, 0, -2, -7, 0,
    -3, 0, 0, 0, 0, -3, 4, -45,
    -25, -2, -26, -14, -29, -7, -7, -17,
    -24, -25, -9, -19, -15, -19, -15, -22,
    0, 0, 0, 0, -7, 0, -2, 0,
    -15, 0, 0, -2, -2, 0, 0, 0,
    0, -10, 0, 0, 0, -3, -3, 0,
    0, 0, 0, 2, 0, 0, -1, 0,
    -11, 0, 0, 0, -5, 0, -11, -3,
    -10, -14, -22, 0, -17, 0, 0, 0,
    0, -13, -17, 0, 0, -36, -28, -36,
    -31, -21, -21, 0, -17, -31, 0, -17,
    0, 0, -14, 0, 0, 0, 0, 0,
    0, 0, 4, -38, -27, -2, -32, -21,
    -33, -14, -9, -23, -31, -28, -21, -28,
    -24, -22, -24, -29, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 0, -2, 0, 0,
    0, -3, -6, -3, -3, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -3, 0, 0, -4, -9, 0,
    -9, 0, 0, -28, 0, 0, 0, 0,
    0, 5, -7, 5, 0, 0, 0, 0,
    0, 0, 0, -10, 24, 0, 0, -21,
    -18, -33, -29, 0, -28, 0, -26, 0,
    0, 0, 0, 0, 2, 0, 30, 0,
    0, 0, -3, -2, -7, 0, 10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -19, 11, 0, -7, -28, -35, -52,
    -49, 0, -38, 0, 0, 0, -5, 0,
    -17, 0, 0, 0, 0, 0, -7, -6,
    -17, -16, -25, 0, -20, 0, 0, -21,
    0, 0, 0, 0, -15, 0, -3, 0,
    0, 0, 0, -1, 0, 2, -2, -4,
    -6, 0, 0, -19, -18, -34, -31, 0,
    -28, 0, -15, 0, 0, 0, 0, 0,
    -3, 0, 0, -1, -6, 0, 0, 0,
    -6, 0, -3, 0, 0, -14, 0, 0,
    0, -4, -9, -9, 3, -9, -6, -1,
    -1, -1, 0, -14, -7, 0, -8, -3,
    -2, -21, -13, -26, -22, -7, -26, 0,
    -7, -17, 0, -2, 0, 0, 0, 0,
    0, 0, -3, 0, -2, -2, 0, -6,
    -3, 0, 0, -2, 0, 0, 0, 0,
    -1, -1, 0, -1, 0, 0, 0, 0,
    0, -3, -4, 0, -7, 0, 0, -14,
    -10, -23, -21, -7, -21, 0, -2, -9,
    0, -3, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, -7, 0, -4, 0, -1, -7, 0,
    -2, 0, 0, 0, 0, -2, 0, 0,
    0, 0, -1, 0, 0, -7, -8, -14,
    -12, 0, -14, 0, -2, 0, -3, 0,
    0, 0, -3, 0, 0, 0, -6, 0,
    0, -3, -3, 0, -3, 0, 0, -7,
    0, 0, 0, -5, -7, -9, 0, -9,
    -3, 0, 0, -1, 0, -6, -3, 0,
    -7, 0, 0, -17, -10, -23, -20, -7,
    -25, 0, -7, -14, -2, -3, 0, -2,
    0, 0, 0, 0, -3, 0, 0, 0,
    -2, 0, -2, 0, 13, 27, 0, 0,
    0, 31, 18, -12, -4, -12, -6, 0,
    0, 12, 0, -2, 6, 0, 8, 13,
    8, 19, 13, 19, 19, 16, 19, 13,
    33, -7, 0, -3, -5, 0, -2, 8,
    8, 0, 0, 0, 0, -3, 3, 0,
    3, 0, 0, 0, 0, -7, 0, 0,
    0, 0, -1, 0, 4, 0, 0, -2,
    0, 0, -2, 0, 17, -2, 0, -9,
    -11, -14, -14, 0, -23, 0, -5, 0,
    -6, -4, -3, 0, -3, 0, 6, 0,
    0, 0, 0, 0, 1, 0, 3, 0,
    0, -19, 0, 0, 0, -4, -23, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    -2, -1, -7, 0, 0, -20, -11, -29,
    -27, 0, -24, 0, -17, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -5, 0,
    -1, -1, -9, 0, -9, 0, -2, -2,
    0, -2, 0, 0, -2, 0, 0, 0,
    0, 0, 0, -2, 0, 0, -2, 0,
    0, 0, 0, -7, -7, -7, -7, 0,
    -7, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    -5, 0, -5, 0, 0, -2, 0, -2,
    0, 0, -2, -5, 0, -5, -4, -4,
    -4, -1, 0, -4, -2, -1, 0, 0,
    0, 0, -8, -6, -6, -3, -7, 0,
    0, -11, 0, -1, 0, 0, -3, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    -1, 0, -7, 0, 0, -1, -1, -2,
    0, 5, -2, -1, -7, 0, 0, -14,
    -14, -23, -19, 2, -24, 3, -1, 0,
    0, 0, -6, 0, 0, 0, 0, 0,
    -3, -2, 0, 0, 0, 0, 0, 0,
    0, -8, 0, -1, 0, 3, -7, 10,
    0, 4, 3, 0, 11, -1, 0, -4,
    -7, 0, 11, -5, 0, -17, -14, -28,
    -23, -2, -28, 0, -4, -1, -3, -2,
    0, 0, 0, 0, 12, 0, 0, 0,
    0, 0, 0, 0, 6, 0, 0, 2,
    0, -4, 0, -8, 5, -30, -10, -30,
    -17, -3, -3, -2, -4, -19, -8, 0,
    -10, -5, 0, -2, -10, -16, -14, -19,
    -17, 0, 0, -23, -3, -3, -4, 0,
    -4, 0, 0, 0, 0, -3, 6, 0,
    3, 0, 3, 0, 0, -7, 0, 0,
    0, -4, -4, -7, 0, -7, 0, 0,
    0, 0, 0, -3, -7, 0, -9, -3,
    0, -11, -8, -24, -21, 0, -30, 0,
    -8, -13, 0, -3, 0, -1, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 0, 0, 2, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 0,
    0, 5, 0, 0, -3, 0, 0, 0,
    -4, -14, -10, 0, -15, 2, 0, 4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 2, 0, 3, 0, 0, 0,
    0, -13, 0, 0, 0, -7, -12, 0,
    0, 0, 0, -3, -3, 0, 0, 0,
    0, -4, -9, 0, 0, -13, -13, -27,
    -24, 0, -25, 0, -7, 0, 0, 0,
    -2, 0, -3, 0, -2, 0, -4, 0,
    0, 0, -7, 0, -7, 0, 0, -1,
    0, -5, 0, -12, -1, -32, -4, -32,
    -14, -1, -1, -2, -3, -23, -9, 0,
    -13, -7, 0, -4, -8, -17, -14, -17,
    -19, -2, 0, -27, -5, -7, -6, 0,
    -6, 0, 0, 0, 0, -3, 2, 0,
    4, 0, 4, 0, 0, 0, 0, 0,
    0, -4, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 2, 0, -3, -6, 0,
    0, -10, -10, -24, -17, 0, -22, 0,
    0, 0, -4, 0, -6, 0, -3, 0,
    0, 0, -6, 0, 0, 0, 0, 2,
    0, 0, 0, 0, 0, 0, 0, -3,
    -2, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -5,
    -9, -21, -18, 0, -20, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0
};


/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 56,
    .right_class_cnt     = 46,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t lv_font_SourceHanSerifSC_Regular_20 = {
#else
lv_font_t lv_font_SourceHanSerifSC_Regular_20 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 20,          /*The maximum line height required by the font*/
    .base_line = 3,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
    .user_data = NULL,
};



#endif /*#if LV_FONT_SOURCEHANSERIFSC_REGULAR_20*/

