/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#ifndef GUI_GUIDER_H
#define GUI_GUIDER_H
#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl.h"

typedef struct
{
  
	lv_obj_t *screen_1;
	bool screen_1_del;
	lv_obj_t *screen_1_cont_1;
	lv_obj_t *screen_1_label_3;
	lv_obj_t *screen_1_label_2;
	lv_obj_t *screen_1_label_1;
	lv_obj_t *screen_3;
	bool screen_3_del;
	lv_obj_t *screen_3_label_1;
	lv_obj_t *screen_3_label_2;
	lv_obj_t *screen_3_label_3;
	lv_obj_t *screen_3_line_1;
	lv_obj_t *screen_3_cont_1;
	lv_obj_t *screen_3_label_4;
	lv_obj_t *screen_3_cont_2;
	lv_obj_t *screen_3_label_5;
	lv_obj_t *screen_3_label_9;
	lv_obj_t *screen_3_label_15;
	lv_obj_t *screen_3_cont_3;
	lv_obj_t *screen_3_label_6;
	lv_obj_t *screen_3_label_10;
	lv_obj_t *screen_3_label_16;
	lv_obj_t *screen_3_cont_4;
	lv_obj_t *screen_3_label_7;
	lv_obj_t *screen_3_label_11;
	lv_obj_t *screen_3_label_17;
	lv_obj_t *screen_3_cont_5;
	lv_obj_t *screen_3_label_8;
	lv_obj_t *screen_3_label_14;
	lv_obj_t *screen_3_label_18;
	lv_obj_t *screen_3_btn_1;
	lv_obj_t *screen_3_btn_1_label;
	lv_obj_t *screen_2;
	bool screen_2_del;
	lv_obj_t *screen_2_label_1;
	lv_obj_t *screen_2_cont_1;
	lv_obj_t *screen_2_btn_7;
	lv_obj_t *screen_2_btn_7_label;
	lv_obj_t *screen_2_btn_6;
	lv_obj_t *screen_2_btn_6_label;
	lv_obj_t *screen_2_btn_5;
	lv_obj_t *screen_2_btn_5_label;
	lv_obj_t *screen_2_btn_4;
	lv_obj_t *screen_2_btn_4_label;
	lv_obj_t *screen_2_btn_3;
	lv_obj_t *screen_2_btn_3_label;
	lv_obj_t *screen_2_btn_2;
	lv_obj_t *screen_2_btn_2_label;
	lv_obj_t *screen_2_btn_1;
	lv_obj_t *screen_2_btn_1_label;
	lv_obj_t *screen_4;
	bool screen_4_del;
	lv_obj_t *screen_4_label_1;
	lv_obj_t *screen_4_btn_1;
	lv_obj_t *screen_4_btn_1_label;
	lv_obj_t *screen_4_label_2;
	lv_obj_t *screen_4_ta_1;
	lv_obj_t *screen_4_btn_2;
	lv_obj_t *screen_4_btn_2_label;
	lv_obj_t *screen_4_btn_3;
	lv_obj_t *screen_4_btn_3_label;
	lv_obj_t *screen_4_btn_4;
	lv_obj_t *screen_4_btn_4_label;
	lv_obj_t *screen_4_btn_5;
	lv_obj_t *screen_4_btn_5_label;
	lv_obj_t *screen_4_btn_6;
	lv_obj_t *screen_4_btn_6_label;
	lv_obj_t *screen_4_btn_7;
	lv_obj_t *screen_4_btn_7_label;
	lv_obj_t *screen_4_btn_8;
	lv_obj_t *screen_4_btn_8_label;
	lv_obj_t *screen_4_btn_9;
	lv_obj_t *screen_4_btn_9_label;
	lv_obj_t *screen_4_btn_10;
	lv_obj_t *screen_4_btn_10_label;
	lv_obj_t *screen_4_btn_12;
	lv_obj_t *screen_4_btn_12_label;
	lv_obj_t *screen_4_btn_13;
	lv_obj_t *screen_4_btn_13_label;
	lv_obj_t *screen_4_btn_14;
	lv_obj_t *screen_4_btn_14_label;
	lv_obj_t *screen_4_msgbox_1;
	lv_obj_t *screen_m1;
	bool screen_m1_del;
	lv_obj_t *screen_m1_label_1;
	lv_obj_t *screen_m1_btn_1;
	lv_obj_t *screen_m1_btn_1_label;
	lv_obj_t *screen_m2;
	bool screen_m2_del;
	lv_obj_t *screen_m2_label_1;
	lv_obj_t *screen_m2_btn_1;
	lv_obj_t *screen_m2_btn_1_label;
	lv_obj_t *screen_m3;
	bool screen_m3_del;
	lv_obj_t *screen_m3_label_1;
	lv_obj_t *screen_m3_cont_1;
	lv_obj_t *screen_m3_btn_6;
	lv_obj_t *screen_m3_btn_6_label;
	lv_obj_t *screen_m3_btn_5;
	lv_obj_t *screen_m3_btn_5_label;
	lv_obj_t *screen_m3_btn_4;
	lv_obj_t *screen_m3_btn_4_label;
	lv_obj_t *screen_m3_btn_3;
	lv_obj_t *screen_m3_btn_3_label;
	lv_obj_t *screen_m3_btn_2;
	lv_obj_t *screen_m3_btn_2_label;
	lv_obj_t *screen_m3_btn_1;
	lv_obj_t *screen_m3_btn_1_label;
	lv_obj_t *screen_m4;
	bool screen_m4_del;
	lv_obj_t *screen_m4_label_1;
	lv_obj_t *screen_m4_btn_1;
	lv_obj_t *screen_m4_btn_1_label;
	lv_obj_t *screen_m5;
	bool screen_m5_del;
	lv_obj_t *screen_m5_label_1;
	lv_obj_t *screen_m5_btn_1;
	lv_obj_t *screen_m5_btn_1_label;
	lv_obj_t *screen_m6;
	bool screen_m6_del;
	lv_obj_t *screen_m6_label_1;
	lv_obj_t *screen_m6_btn_1;
	lv_obj_t *screen_m6_btn_1_label;
	lv_obj_t *screen_s1;
	bool screen_s1_del;
	lv_obj_t *screen_s1_label_1;
	lv_obj_t *screen_s1_label_2;
	lv_obj_t *screen_s1_ddlist_1;
	lv_obj_t *screen_s1_label_3;
	lv_obj_t *screen_s1_ddlist_2;
	lv_obj_t *screen_s1_label_4;
	lv_obj_t *screen_s1_ddlist_3;
	lv_obj_t *screen_s1_ddlist_4;
	lv_obj_t *screen_s1_label_5;
	lv_obj_t *screen_s1_label_7;
	lv_obj_t *screen_s1_ddlist_5;
	lv_obj_t *screen_s1_label_6;
	lv_obj_t *screen_s1_ddlist_6;
	lv_obj_t *screen_s1_btn_1;
	lv_obj_t *screen_s1_btn_1_label;
	lv_obj_t *screen_s2;
	bool screen_s2_del;
	lv_obj_t *screen_s2_label_1;
	lv_obj_t *screen_s2_btn_1;
	lv_obj_t *screen_s2_btn_1_label;
	lv_obj_t *screen_s3;
	bool screen_s3_del;
	lv_obj_t *screen_s3_label_1;
	lv_obj_t *screen_s3_label_3;
	lv_obj_t *screen_s3_label_4;
	lv_obj_t *screen_s3_btn_1;
	lv_obj_t *screen_s3_btn_1_label;
	lv_obj_t *screen_s3_label_5;
	lv_obj_t *screen_s3_label_6;
	lv_obj_t *screen_s3_slider_1;
	lv_obj_t *screen_s4;
	bool screen_s4_del;
	lv_obj_t *screen_s4_label_1;
	lv_obj_t *screen_s4_label_2;
	lv_obj_t *screen_s4_ddlist_1;
	lv_obj_t *screen_s4_label_3;
	lv_obj_t *screen_s4_label_4;
	lv_obj_t *screen_s4_btn_1;
	lv_obj_t *screen_s4_btn_1_label;
	lv_obj_t *screen_s4_ddlist_2;
	lv_obj_t *screen_s4_ddlist_3;
	lv_obj_t *screen_11;
	bool screen_11_del;
	lv_obj_t *screen_11_label_1;
	lv_obj_t *screen_11_btn_1;
	lv_obj_t *screen_11_btn_1_label;
	lv_obj_t *screen_15;
	bool screen_15_del;
	lv_obj_t *screen_15_label_1;
	lv_obj_t *screen_15_chart_1;
	lv_chart_series_t *screen_15_chart_1_0;
	lv_chart_series_t *screen_15_chart_1_1;
	lv_chart_series_t *screen_15_chart_1_2;
	lv_obj_t *screen_s5;
	bool screen_s5_del;
	lv_obj_t *screen_s5_btn_1;
	lv_obj_t *screen_s5_btn_1_label;
	lv_obj_t *screen_s5_btn_2;
	lv_obj_t *screen_s5_btn_2_label;
	lv_obj_t *screen_s5_label_1;
	lv_obj_t *screen_s5_label_2;
}lv_ui;

typedef void (*ui_setup_scr_t)(lv_ui * ui);

void ui_init_style(lv_style_t * style);

void ui_load_scr_animation(lv_ui *ui, lv_obj_t ** new_scr, bool new_scr_del, bool * old_scr_del, ui_setup_scr_t setup_scr,
                           lv_scr_load_anim_t anim_type, uint32_t time, uint32_t delay, bool is_clean, bool auto_del);

void ui_animation(void * var, int32_t duration, int32_t delay, int32_t start_value, int32_t end_value, lv_anim_path_cb_t path_cb,
                       uint16_t repeat_cnt, uint32_t repeat_delay, uint32_t playback_time, uint32_t playback_delay,
                       lv_anim_exec_xcb_t exec_cb, lv_anim_start_cb_t start_cb, lv_anim_ready_cb_t ready_cb, lv_anim_deleted_cb_t deleted_cb);


void init_scr_del_flag(lv_ui *ui);

void setup_ui(lv_ui *ui);

void init_keyboard(lv_ui *ui);

extern lv_ui guider_ui;


void setup_scr_screen_1(lv_ui *ui);
void setup_scr_screen_3(lv_ui *ui);
void setup_scr_screen_2(lv_ui *ui);
void setup_scr_screen_4(lv_ui *ui);
void setup_scr_screen_m1(lv_ui *ui);
void setup_scr_screen_m2(lv_ui *ui);
void setup_scr_screen_m3(lv_ui *ui);
void setup_scr_screen_m4(lv_ui *ui);
void setup_scr_screen_m5(lv_ui *ui);
void setup_scr_screen_m6(lv_ui *ui);
void setup_scr_screen_s1(lv_ui *ui);
void setup_scr_screen_s2(lv_ui *ui);
void setup_scr_screen_s3(lv_ui *ui);
void setup_scr_screen_s4(lv_ui *ui);
void setup_scr_screen_11(lv_ui *ui);
void setup_scr_screen_15(lv_ui *ui);
void setup_scr_screen_s5(lv_ui *ui);

LV_FONT_DECLARE(lv_font_SourceHanSerifSC_Regular_20)
LV_FONT_DECLARE(lv_font_SourceHanSerifSC_Regular_16)
LV_FONT_DECLARE(lv_font_SourceHanSerifSC_Regular_48)
LV_FONT_DECLARE(lv_font_SourceHanSerifSC_Regular_12)


#ifdef __cplusplus
}
#endif
#endif
