/*
* Copyright 2025 NXP
* NXP Proprietary. This software is owned or controlled by NXP and may only be used strictly in
* accordance with the applicable license terms. By expressly accepting such terms or by downloading, installing,
* activating and/or otherwise using the software, you are agreeing that you have read, and that you agree to
* comply with and are bound by, such license terms.  If you do not agree to be bound by the applicable license
* terms, then you may not retain, install, activate or otherwise use the software.
*/

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
#include "widgets_init.h"
#include "custom.h"



void setup_scr_screen_15(lv_ui *ui)
{
    //Write codes screen_15
    ui->screen_15 = lv_obj_create(NULL);
    lv_obj_set_size(ui->screen_15, 320, 240);
    lv_obj_set_scrollbar_mode(ui->screen_15, LV_SCROLLBAR_MODE_OFF);

    //Write style for screen_15, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->screen_15, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->screen_15, lv_color_hex(0x909092), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->screen_15, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write codes screen_15_label_1
    ui->screen_15_label_1 = lv_label_create(ui->screen_15);
    lv_label_set_text(ui->screen_15_label_1, "数据表");
    lv_label_set_long_mode(ui->screen_15_label_1, LV_LABEL_LONG_WRAP);
    lv_obj_set_pos(ui->screen_15_label_1, 0, 0);
    lv_obj_set_size(ui->screen_15_label_1, 320, 21);

    //Write style for screen_15_label_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_border_width(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(ui->screen_15_label_1, lv_color_hex(0x000000), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->screen_15_label_1, &lv_font_SourceHanSerifSC_Regular_20, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->screen_15_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_letter_space(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_line_space(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_text_align(ui->screen_15_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(ui->screen_15_label_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->screen_15_label_1, lv_color_hex(0xbabbbb), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->screen_15_label_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_top(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_right(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_bottom(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_pad_left(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for screen_15_label_1, Part: LV_PART_MAIN, State: LV_STATE_DISABLED.
    lv_obj_set_style_border_width(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_radius(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_color(ui->screen_15_label_1, lv_color_hex(0x000000), LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_font(ui->screen_15_label_1, &lv_font_SourceHanSerifSC_Regular_20, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_opa(ui->screen_15_label_1, 255, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_letter_space(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_line_space(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_text_align(ui->screen_15_label_1, LV_TEXT_ALIGN_CENTER, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_bg_opa(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_top(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_right(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_bottom(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_pad_left(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);
    lv_obj_set_style_shadow_width(ui->screen_15_label_1, 0, LV_PART_MAIN|LV_STATE_DISABLED);

    //Write codes screen_15_chart_1
    ui->screen_15_chart_1 = lv_chart_create(ui->screen_15);
    lv_chart_set_type(ui->screen_15_chart_1, LV_CHART_TYPE_LINE);
    lv_chart_set_div_line_count(ui->screen_15_chart_1, 10, 10);
    lv_chart_set_point_count(ui->screen_15_chart_1, 20);
    lv_chart_set_range(ui->screen_15_chart_1, LV_CHART_AXIS_PRIMARY_Y, 0, 100);
    lv_chart_set_axis_tick(ui->screen_15_chart_1, LV_CHART_AXIS_PRIMARY_Y, 10, 5, 5, 4, true, 40);
    lv_chart_set_range(ui->screen_15_chart_1, LV_CHART_AXIS_SECONDARY_Y, 0, 100);
    lv_chart_set_axis_tick(ui->screen_15_chart_1, LV_CHART_AXIS_PRIMARY_X, 12, 8, 5, 4, false, 40);
    lv_chart_set_zoom_x(ui->screen_15_chart_1, 256);
    lv_chart_set_zoom_y(ui->screen_15_chart_1, 256);
    ui->screen_15_chart_1_0 = lv_chart_add_series(ui->screen_15_chart_1, lv_color_hex(0x22ff00), LV_CHART_AXIS_PRIMARY_Y);
#if LV_USE_FREEMASTER == 0
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 1);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 20);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 30);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 40);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 5);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 30);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
    lv_chart_set_next_value(ui->screen_15_chart_1, ui->screen_15_chart_1_0, 0);
#endif
    ui->screen_15_chart_1_1 = lv_chart_add_series(ui->screen_15_chart_1, lv_color_hex(0xebff00), LV_CHART_AXIS_PRIMARY_Y);
#if LV_USE_FREEMASTER == 0
#endif
    ui->screen_15_chart_1_2 = lv_chart_add_series(ui->screen_15_chart_1, lv_color_hex(0x000000), LV_CHART_AXIS_PRIMARY_Y);
#if LV_USE_FREEMASTER == 0
#endif
    lv_obj_set_pos(ui->screen_15_chart_1, 0, 21);
    lv_obj_set_size(ui->screen_15_chart_1, 320, 190);
    lv_obj_set_scrollbar_mode(ui->screen_15_chart_1, LV_SCROLLBAR_MODE_OFF);

    //Write style for screen_15_chart_1, Part: LV_PART_MAIN, State: LV_STATE_DEFAULT.
    lv_obj_set_style_bg_opa(ui->screen_15_chart_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(ui->screen_15_chart_1, lv_color_hex(0x080907), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_bg_grad_dir(ui->screen_15_chart_1, LV_GRAD_DIR_NONE, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_width(ui->screen_15_chart_1, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_opa(ui->screen_15_chart_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(ui->screen_15_chart_1, lv_color_hex(0xe8e8e8), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_border_side(ui->screen_15_chart_1, LV_BORDER_SIDE_FULL, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_radius(ui->screen_15_chart_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_width(ui->screen_15_chart_1, 1, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->screen_15_chart_1, lv_color_hex(0xe8e8e8), LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->screen_15_chart_1, 255, LV_PART_MAIN|LV_STATE_DEFAULT);
    lv_obj_set_style_shadow_width(ui->screen_15_chart_1, 0, LV_PART_MAIN|LV_STATE_DEFAULT);

    //Write style for screen_15_chart_1, Part: LV_PART_TICKS, State: LV_STATE_DEFAULT.
    lv_obj_set_style_text_color(ui->screen_15_chart_1, lv_color_hex(0x020202), LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(ui->screen_15_chart_1, &lv_font_SourceHanSerifSC_Regular_20, LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(ui->screen_15_chart_1, 255, LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_line_width(ui->screen_15_chart_1, 2, LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_line_color(ui->screen_15_chart_1, lv_color_hex(0x0b2012), LV_PART_TICKS|LV_STATE_DEFAULT);
    lv_obj_set_style_line_opa(ui->screen_15_chart_1, 255, LV_PART_TICKS|LV_STATE_DEFAULT);

    //The custom code of screen_15.


    //Update current screen layout.
    lv_obj_update_layout(ui->screen_15);

}
